var sampleOperationResp = require('./utils/operationResp');
var HttpStatus = require('http-status-codes');
var db_resp = require('./utils/db_resp');
const pagination_filters_utils = require('../api_models/utils/pagination_filters_utils');
const users_model = require('./users_model');

const JSONStream = require('JSONStream');
const jsonToCsv = require('json-to-csv-stream');
const fs = require('fs');
const path = require('path');
const nodemailer = require('nodemailer');
const { json } = require('body-parser');
const { allQueues } = require('./queues_v2/queues');
const { convertSecondsToReadableTimePeriod } = require('./utils/common');
const { getSignedUrl, uploadStreamToS3 } = require('./utils/s3_helper');
const { Parser } = require('json2csv');
const AWS = require('aws-sdk');
const moment = require('moment');
const {
    setParamsToFeatureFlagModel,
    setParamsToDumpsExportsModel,
} = require('./queues/processors/helpers');
const {
    dumpExportReqCounter,
    dumpExportFailureCounter,
    dumpExportSuccessCounter,
    dumpExportsCounter,
    dumpExportStatus,
} = require('./utils/metrics');
const { moduleKeys } = require('./utils/helper');

const getAttachments = async (path, filename, bucketName, getLink = false) => {
    try {
        let attachments = [];
        const s3Region = process.env.S3_REGION;
        if (path.startsWith(`https://${bucketName}.s3`)) {
            let s3Key = path.replace(
                `https://${bucketName}.s3.${s3Region}.amazonaws.com/`,
                ''
            );

            if (getLink) {
                // Make the object public and get the public URL
                const fileUrl = `https://static.wify.co.in/${s3Key}`;

                attachments.push({
                    filename: filename,
                    path: fileUrl,
                });
            } else {
                // Generate a signed URL as before
                const signedUrl = await getSignedUrl({ s3Key });

                attachments.push({
                    filename: filename,
                    path: signedUrl,
                });
            }
        } else {
            console.log(
                'attendance_model :: getAttachments :: invalid s3Url ::',
                path
            );
        }

        return attachments;
    } catch (error) {
        console.log('attendance_model :: getAttachments :: error ::', error);
        return [];
    }
};

async function getEmailJobData(
    jobData,
    data,
    fileName,
    bucketName,
    org_id,
    getLink = false,
    isPartial = false,
    failure = false,
    isFeatureEnabled = false,
    getFileUrlLink = false
) {
    // Call getAttachments with the getLink flag to either get a public URL or signed URL
    const attachments = await getAttachments(
        data.Location,
        fileName,
        bucketName,
        getLink
    );

    if (isFeatureEnabled) {
        // Case 1: Data cannot be processed at all
        if (failure && !isPartial) {
            message = `
            Dear ${jobData?.requester?.name || 'User'},<br><br>
            The requested data dump could not be processed. Kindly get in touch with us at 
            <a href="mailto:<EMAIL>"><EMAIL></a> to know more.<br><br>
            Regards,<br>
            Wify Support Team
        `;
            return {
                to: jobData?.requester?.email_id,
                subject: 'Data Export Request Failed',
                message,
                org_id,
                usr_id: jobData?.requester?.usr_id,
                ip_address: jobData?.requester?.ip_addr,
                user_agent: jobData?.requester?.user_agent,
            };
        }

        // Case 2 & 3: Generate a download link
        const downloadLink = attachments.length
            ? `<a href="${attachments[0].path}" download>Link</a>`
            : 'No file available.';

        if (isPartial && failure) {
            // Case 3: Partial Data
            message = `
            Dear ${jobData?.requester?.name || 'User'},<br><br>
            Due to system constraints, we could only provide partial data at this time.<br><br>

            <b>Reason(s) for Partial Data:</b>
            <ul>
                <li>The selected date range is too large.</li>
                <li>The data exceeds the maximum allowable row limit.</li>
            </ul>

            <b>To receive the complete data, we recommend:</b>
            <ul>
                <li>Narrowing down the date range and submitting a new request.</li>
                <li>Contacting our support team for assistance with large data exports.</li>
            </ul>
            
            <b>You can download the file from this:</b> ${downloadLink}<br><br>

            Regards,<br>
            Wify Support Team
        `;

            return {
                to: jobData?.requester?.email_id,
                subject: 'Partial Data Export - Action Required',
                message,
                org_id,
                usr_id: jobData?.requester?.usr_id,
                ip_address: jobData?.requester?.ip_addr,
                user_agent: jobData?.requester?.user_agent,
            };
        }

        return {
            to: jobData?.requester?.email_id,
            subject: jobData?.requester?.subject,
            message:
                '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------<br><br>' +
                'You can download the file from this ' +
                `${downloadLink}`,
            fileUrlLink: getFileUrlLink ? attachments[0]?.path : '',
            attachments: getLink ? [] : attachments, // Use the modified attachments
            org_id,
            usr_id: jobData?.requester?.usr_id,
            ip_address: jobData?.requester?.ip_addr,
            user_agent: jobData?.requester?.user_agent,
        };
    } else {
        return {
            to: jobData?.requester?.email_id,
            subject: jobData?.requester?.subject,
            message:
                '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------',
            attachments: attachments, // Use the modified attachments
            org_id,
            usr_id: jobData?.requester?.usr_id,
            ip_address: jobData?.requester?.ip_addr,
            user_agent: jobData?.requester?.user_agent,
        };
    }
}

class attendance_model {
    exportAttendanceByEmail(query) {
        return new Promise(async (resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['userContext'] = this.userContext;

            requester['module'] = moduleKeys.siteAttendance;
            const parsedFilters_ = JSON.parse(filters_);
            requester['date_range'] = parsedFilters_?.days;
            const jobData = { requester, filters_ };

            let dumpResp;
            try {
                // add entry in dumps tracker
                const dumps_exports_model = require('./dumps_tracker_model');
                setParamsToDumpsExportsModel(
                    dumps_exports_model,
                    { ...this, userContext: this.userContext },
                    this.db
                );
                dumpResp =
                    await dumps_exports_model.createEntryForDumpRequest(
                        requester
                    );
                if (!dumpResp.success) {
                    console.log(
                        'attendance_model :: exportAttendanceByEmail :: createEntryForDumpRequest resp :: ',
                        dumpResp
                    );
                }
            } catch (error) {
                console.log(
                    'attendance_model :: exportAttendanceByEmail :: createEntryForDumpRequest error :: ',
                    error
                );
            }
            // pass entry_id to update same recored
            let entryId;
            try {
                const parsedResp = JSON.parse(dumpResp.resp);
                entryId = parsedResp?.entry_id;
                if (entryId) {
                    jobData.dumps_entry_id = entryId;
                }
            } catch {
                console.log(
                    'attendance_model :: exportAttendanceByEmail :: createEntryForDumpRequest error :: ',
                    error
                );
            }

            allQueues.WIFY_SITE_ATTENDANCE_EXPORT_BY_EMAIL.addJob(jobData);

            dumpExportReqCounter.inc({
                module: moduleKeys.siteAttendance,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    async checkRDSHealth(dbInstanceId) {
        const cloudwatch = new AWS.CloudWatch({
            accessKeyId: process.env.CLOUD_WATCH_ACCESS_KEY_ID,
            secretAccessKey: process.env.CLOUD_WATCH_SECRET_ACCESS_KEY,
            region: process.env.S3_REGION,
        });
        const now = new Date();
        const startTime = new Date(now.getTime() - 5 * 60 * 1000); // 5 minutes ago
        const endTime = now; // Current time

        const params = {
            StartTime: startTime.toISOString(), // Required
            EndTime: endTime.toISOString(), // Required
            MetricDataQueries: [
                {
                    Id: 'cpuUtilization',
                    MetricStat: {
                        Metric: {
                            Namespace: 'AWS/RDS',
                            MetricName: 'CPUUtilization',
                            Dimensions: [
                                {
                                    Name: 'DBInstanceIdentifier',
                                    Value: dbInstanceId,
                                },
                            ],
                        },
                        Period: 60, // Last 60 seconds
                        Stat: 'Average',
                    },
                    ReturnData: true,
                },
                {
                    Id: 'databaseConnections',
                    MetricStat: {
                        Metric: {
                            Namespace: 'AWS/RDS',
                            MetricName: 'DatabaseConnections',
                            Dimensions: [
                                {
                                    Name: 'DBInstanceIdentifier',
                                    Value: dbInstanceId,
                                },
                            ],
                        },
                        Period: 60,
                        Stat: 'Average',
                    },
                    ReturnData: true,
                },
                {
                    Id: 'freeableMemory',
                    MetricStat: {
                        Metric: {
                            Namespace: 'AWS/RDS',
                            MetricName: 'FreeableMemory',
                            Dimensions: [
                                {
                                    Name: 'DBInstanceIdentifier',
                                    Value: dbInstanceId,
                                },
                            ],
                        },
                        Period: 60,
                        Stat: 'Average',
                    },
                    ReturnData: true,
                },
                {
                    Id: 'readLatency',
                    MetricStat: {
                        Metric: {
                            Namespace: 'AWS/RDS',
                            MetricName: 'ReadLatency',
                            Dimensions: [
                                {
                                    Name: 'DBInstanceIdentifier',
                                    Value: dbInstanceId,
                                },
                            ],
                        },
                        Period: 60,
                        Stat: 'Average',
                    },
                    ReturnData: true,
                },
            ],
        };

        return true;
        // try {
        //     const response = await cloudwatch.getMetricData(params).promise();
        //     console.log('attendance_model :: processSiteExportByEmail :: RDS Metrics:: response ::', response);

        //     let metrics = {};
        //     response.MetricDataResults.forEach((metric) => {
        //         metrics[metric.Id] = metric.Values.length
        //             ? metric.Values[0]
        //             : 0; // Get latest value
        //     });

        //     console.log('attendance_model :: checkRDSHealth :: RDS Metrics:', metrics);

        //     // Set thresholds
        //     const CPU_THRESHOLD = 80; // Avoid queries if CPU > 80%
        //     const CONNECTIONS_THRESHOLD = 100; // Avoid if too many connections
        //     const MEMORY_THRESHOLD = 500 * 1024 * 1024; // Avoid if memory < 500MB
        //     const LATENCY_THRESHOLD = 0.5; // Avoid if read/write latency > 0.5 sec

        //     if (
        //         metrics.cpuUtilization > CPU_THRESHOLD ||
        //         metrics.databaseConnections > CONNECTIONS_THRESHOLD
        //     ) {
        //         console.warn(
        //             'attendance_model :: checkRDSHealth :: Skipping query execution due to high RDS load.'
        //         );
        //         return false;
        //     }

        //     return true;
        // } catch (error) {
        //     console.error('attendance_model :: checkRDSHealth :: Error fetching RDS metrics:', error);
        //     return false; // Fail-safe: Avoid running the query
        // }
    }

    async updateDumpReqRecord(userContext, data, entry_id) {
        try {
            const dumps_exports_model = require('./dumps_tracker_model');
            setParamsToDumpsExportsModel(
                dumps_exports_model,
                { ...dumps_exports_model, userContext: userContext },
                this.db
            );

            if (entry_id) {
                const dumpResp =
                    await dumps_exports_model.updateDumpsExportRecord(
                        data,
                        entry_id
                    );
                if (!dumpResp.success) {
                    console.log(
                        'attendance_model :: updateDumpReqRecord resp :: ',
                        dumpResp
                    );
                }
            }
        } catch (error) {
            console.log(
                'attendance_model :: updateDumpReqRecord :: error',
                error
            );
        }
    }

    processSiteExportByEmail(jobData) {
        return new Promise(async (resolve, reject) => {
            try {
                // update start_processing_time in dump record
                const { dumps_entry_id, requester } = jobData;
                let dumpProcessPayload = {};
                const startUtcTime = moment
                    .utc()
                    .format('YYYY-MM-DD HH:mm:ss.SSS');
                dumpProcessPayload['start_processing_time'] = startUtcTime;
                await this.updateDumpReqRecord(
                    requester?.userContext,
                    dumpProcessPayload,
                    dumps_entry_id
                );

                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }

                //add feature flag to TMS250226757739 ( Modify TMS dump request email body based on type of dump received.)
                const featureFlagModel = require('./setup/feature_flag_model');
                setParamsToFeatureFlagModel(
                    featureFlagModel,
                    { ...this, userContext: jobData?.requester.userContext },
                    this.db
                );
                const featureFlagResp = await featureFlagModel.getFeatureAccess(
                    jobData.requester,
                    'TMS250321606086'
                );
                const parsedFilters_ = JSON.parse(filters_);
                const total = parsedFilters_?.total || 0;
                const isFeatureEnabled = featureFlagResp.resp;
                if (isFeatureEnabled) {
                    console.log(
                        'processSiteExportByEmail :: requesterInfo',
                        requesterInfo,
                        filters_
                    );

                    const org_id = jobData?.requester?.org_id;
                    const d = new Date();
                    const today = d.toISOString().slice(0, 10);
                    const fileName = `Site_attendance_dump_${today}_${d.getTime()}.csv`;
                    const s3Key = `dumps2025/attendance/org_id_${org_id}/${fileName}`;
                    const bucketName = process.env.S3_STATIC_BUCKET;
                    let failure = false;
                    let isPartial = false;
                    // Create a pass-through stream to upload to S3
                    const { writeStream, uploadPromise } = uploadStreamToS3({
                        bucketName,
                        s3Key,
                        contentType: 'text/csv',
                    });
                    // Convert JSON data to CSV using json2csv
                    let json2csvParser = new Parser({ header: true });

                    let chunkSize;
                    if (total > 50000) {
                        chunkSize = 10000;
                    } else if (total > 10000) {
                        chunkSize = 5000;
                    } else {
                        chunkSize = 1000; // Small datasets get smaller chunks
                    }
                    let isFirstBatch = true;
                    let pageNo = 1;

                    const fetchAndProcessBatch = async (
                        offset = 0,
                        pageNo = 1,
                        attempt = 1
                    ) => {
                        if (offset >= total) {
                            console.log(
                                'attendance_model :: processSiteExportByEmail :: No more data to process.'
                            );
                            writeStream.end();
                            return;
                        }
                        console.log(
                            `attendance_model :: processSiteExportByEmail :: fetchAndProcessBatch :: Fetching records from offset ${offset}...`
                        );
                        // ✅ Check RDS health before querying
                        const isHealthy = await this.checkRDSHealth(
                            process.env.DUMP_DB
                        );
                        if (!isHealthy) {
                            console.log(
                                'attendance_model :: processSiteExportByEmail :: fetchAndProcessBatch :: RDS is under high load. Retrying in 30 seconds...'
                            );
                            await new Promise((resolve) =>
                                setTimeout(resolve, 30000)
                            ); // Wait 30 seconds
                            return fetchAndProcessBatch(
                                offset,
                                pageNo,
                                attempt
                            ); // Retry fetching
                        }
                        const finalFilters = JSON.stringify({
                            ...parsedFilters_,
                            offset,
                            pageNo,
                            limit: chunkSize,
                        });
                        console.log(
                            `attendance_model :: processSiteExportByEmail :: fetchAndProcessBatch :: Processing chunkSize of ${chunkSize} records...`
                        );
                        try {
                            const data =
                                await dbObj.tms_get_attendance_req_dumps_fr_usr_as_array(
                                    requesterInfo,
                                    finalFilters
                                );

                            if (
                                !data.length ||
                                !data[0]
                                    ?.tms_get_attendance_req_dumps_fr_usr_as_array
                                    ?.length
                            ) {
                                console.log(
                                    'attendance_model :: processSiteExportByEmail :: fetchAndProcessBatch :: No more data to process.'
                                );
                                writeStream.end();
                                return;
                            }

                            const chunk =
                                data[0]
                                    .tms_get_attendance_req_dumps_fr_usr_as_array;
                            console.log(
                                `attendance_model :: processSiteExportByEmail :: fetchAndProcessBatch :: Processed chunk of ${chunk.length} records...`
                            );

                            let csvData;
                            if (isFirstBatch) {
                                csvData = json2csvParser.parse(chunk);
                                isFirstBatch = false;
                            } else {
                                json2csvParser = new Parser({ header: false });
                                csvData = json2csvParser.parse(chunk);
                            }

                            writeStream.write(csvData + '\n');
                            await new Promise((resolve) =>
                                setTimeout(resolve, 20000)
                            );
                            await fetchAndProcessBatch(
                                offset + chunkSize,
                                pageNo + 1,
                                1
                            );
                        } catch (error) {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.siteAttendance,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            console.error(
                                'processSiteExportByEmail :: fetchAndProcessBatch :: Error fetching data:',
                                error
                            );
                            console.log(
                                `processSiteExportByEmail :: fetchAndProcessBatch :: DB Timeout detected (Attempt ${attempt}).`
                            );

                            if (attempt >= 5) {
                                // update failure status in dump record
                                let failPayload = {};
                                const endUtcTime = moment
                                    .utc()
                                    .format('YYYY-MM-DD HH:mm:ss.SSS');
                                failPayload['end_processing_time'] = endUtcTime;
                                failPayload['status'] = 'failed';
                                await this.updateDumpReqRecord(
                                    requester?.userContext,
                                    failPayload,
                                    dumps_entry_id
                                );

                                console.log(
                                    'processSiteExportByEmail :: fetchAndProcessBatch :: Max retries reached. Aborting export.'
                                );
                                failure = true;
                                isPartial = offset > 0 ? true : false;
                                writeStream.end();
                                return;
                            }

                            await new Promise((resolve) =>
                                setTimeout(resolve, 300000)
                            );

                            return fetchAndProcessBatch(
                                offset,
                                pageNo,
                                attempt + 1
                            );
                        }
                    };

                    await fetchAndProcessBatch();

                    // Wait for the S3 upload to complete
                    const uploadResult = await uploadPromise;

                    // Prepare email job data
                    const emailJobData = await getEmailJobData(
                        jobData,
                        uploadResult,
                        fileName,
                        bucketName,
                        org_id,
                        true,
                        isPartial,
                        failure,
                        isFeatureEnabled,
                        true
                    );
                    console.log(
                        'processSiteExportByEmail :: fetchAndProcessBatch :: emailJobData',
                        emailJobData
                    );
                    allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
                    // update sucess in dump record
                    let successPayload = {};
                    const endUtcTime = moment
                        .utc()
                        .format('YYYY-MM-DD HH:mm:ss.SSS');
                    successPayload['end_processing_time'] = endUtcTime;
                    successPayload['status'] = 'success';
                    successPayload['file_rows'] = total;
                    successPayload['dump_file_url'] = emailJobData?.fileUrlLink;
                    await this.updateDumpReqRecord(
                        requester?.userContext,
                        successPayload,
                        dumps_entry_id
                    );
                    dumpExportSuccessCounter.inc({
                        module: moduleKeys.siteAttendance,
                    });
                    dumpExportsCounter.inc({
                        status: dumpExportStatus.success,
                    });
                    return new sampleOperationResp(
                        true,
                        'Added to email queue',
                        HttpStatus.StatusCodes.OK
                    );
                } else {
                    dbObj
                        .tms_get_attendance_req_dumps_fr_usr_as_array(
                            requesterInfo,
                            filters_,
                            { stream: true }
                        )
                        .then(
                            (stream) => {
                                // we need to start streaming the incoming data
                                // we need to start streaming the incoming data
                                // we need to start streaming the incoming data
                                // we need to start streaming the incoming data
                                // we need to start streaming the incoming data
                                // we need to start streaming the incoming data
                                // we need to start streaming the incoming data
                                // and save to temp folder
                                // once saved trigger email
                                const org_id = jobData?.requester?.org_id;
                                const d = new Date(); // today now
                                const today = d.toISOString().slice(0, 10); // YYYY-MM-DD

                                const fileName = `Site_attendance_dump_${today}_${d.getTime()}.csv`;
                                const s3Key = `dump/attendance/org_id_${org_id}/${fileName}`;
                                const bucketName = process.env.S3_BUCKET;

                                const { writeStream, uploadPromise } =
                                    uploadStreamToS3({
                                        bucketName,
                                        s3Key,
                                        contentType: 'text/csv',
                                    });

                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_attendance_req_dumps_fr_usr_as_array',
                                        })
                                    )
                                    .pipe(writeStream)
                                    .on('finish', async () => {
                                        try {
                                            const data = await uploadPromise;

                                            const emailJobData =
                                                await getEmailJobData(
                                                    jobData,
                                                    data,
                                                    fileName,
                                                    bucketName,
                                                    org_id
                                                );
                                            console.log(
                                                'emailJobData',
                                                emailJobData
                                            );
                                            allQueues.WIFY_SEND_EMAIL.addJob(
                                                emailJobData
                                            );

                                            // update sucess in dump record
                                            let successPayload = {};
                                            const endUtcTime = moment
                                                .utc()
                                                .format(
                                                    'YYYY-MM-DD HH:mm:ss.SSS'
                                                );
                                            successPayload[
                                                'end_processing_time'
                                            ] = endUtcTime;
                                            successPayload['status'] =
                                                'success';
                                            successPayload['file_rows'] = total;
                                            successPayload['dump_file_url'] =
                                                emailJobData?.attachments?.[0]?.path;
                                            await this.updateDumpReqRecord(
                                                requester?.userContext,
                                                successPayload,
                                                dumps_entry_id
                                            );
                                            dumpExportSuccessCounter.inc({
                                                module: moduleKeys.siteAttendance,
                                            });
                                            dumpExportsCounter.inc({
                                                status: dumpExportStatus.success,
                                            });

                                            resolve(
                                                new sampleOperationResp(
                                                    true,
                                                    'Added to email queue',
                                                    HttpStatus.StatusCodes.OK
                                                )
                                            );
                                        } catch (error) {
                                            dumpExportFailureCounter.inc({
                                                module: moduleKeys.siteAttendance,
                                            });
                                            dumpExportsCounter.inc({
                                                status: dumpExportStatus.failure,
                                            });
                                            console.error(
                                                'attendance_model :: processSiteExportByEmail :: error :: S3 upload failed:',
                                                error
                                            );
                                            reject(
                                                new sampleOperationResp(
                                                    false,
                                                    'Failed to upload file to S3',
                                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                                )
                                            );
                                        }
                                    })
                                    .on('error', (err) => {
                                        dumpExportFailureCounter.inc({
                                            module: moduleKeys.siteAttendance,
                                        });
                                        dumpExportsCounter.inc({
                                            status: dumpExportStatus.failure,
                                        });
                                        console.error(
                                            'attendance_model :: processSiteExportByEmail :: error :: File write error:',
                                            err
                                        );
                                    });
                            },
                            async (err) => {
                                // update failure status in dump record
                                let failPayload = {};
                                const endUtcTime = moment
                                    .utc()
                                    .format('YYYY-MM-DD HH:mm:ss.SSS');
                                failPayload['end_processing_time'] = endUtcTime;
                                failPayload['status'] = 'failed';
                                await this.updateDumpReqRecord(
                                    requester?.userContext,
                                    failPayload,
                                    dumps_entry_id
                                );
                                this.fatalDbError(resolve, err);
                            }
                        );
                }
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.siteAttendance,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                console.log('processSiteExportByEmail :: Error', error);
                this.fatalDbError(resolve, error);
            }
        });
    }

    getViewDataFrAvailabilityForm(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_availability_getview_data(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_availability_getview_data
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAvailabilityOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_availability_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_availability_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    createOrUpdateAttendance(query, entry_id = 0) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            var form_data = JSON.stringify(query);
            if (!this.validateAttendanceForm(form_data)) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'Please fill mandatory * field.',
                        HttpStatus.StatusCodes.BAD_REQUEST
                    )
                );
                return;
            }
            if (!this.db) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            this.db.tms_create_attendance(form_data, entry_id).then(
                (res) => {
                    var attendanceViewResp = new db_resp(
                        res[0].tms_create_attendance
                    );
                    if (!attendanceViewResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(attendanceViewResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAllAvailability(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;
        // console.log("requester getAllAvailability",JSON.stringify(requester));
        return new Promise((resolve, reject) => {
            //added new parameter
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_availability(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(res[0].tms_get_availability);
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getAllMyleaves(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;

        return new Promise((resolve, reject) => {
            //added new parameter

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_myleaves(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(res[0].tms_get_myleaves);
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getMyLeavesOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_myleaves_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_myleaves_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    // Capacity-wise Availability Methods
    getCapacityWiseAvailabilityOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_myleaves_overview_proto(form_data)
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_myleaves_overview_proto
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getAllCapacityWiseAvailability(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;

        return new Promise((resolve, reject) => {
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_myleaves(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_myleaves
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    exportCapacityWiseAvailabilityByEmail(query) {
        return new Promise(async (resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['userContext'] = this.userContext;

            requester['module'] = moduleKeys.capacityWiseAvailability;
            const parsedFilters_ = JSON.parse(filters_);
            requester['date_range'] = parsedFilters_?.days;
            const jobData = { requester, filters_ };

            let dumpResp;
            try {
                // add entry in dumps tracker
                const dumps_exports_model = require('./dumps_tracker_model');
                setParamsToDumpsExportsModel(
                    dumps_exports_model,
                    { ...this, userContext: this.userContext },
                    this.db
                );
                dumpResp =
                    await dumps_exports_model.createEntryForDumpRequest(
                        requester
                    );
                if (!dumpResp.success) {
                    console.log(
                        'attendance_model :: exportCapacityWiseAvailabilityByEmail :: createEntryForDumpRequest resp :: ',
                        dumpResp
                    );
                }
            } catch (error) {
                console.log(
                    'attendance_model :: exportCapacityWiseAvailabilityByEmail :: createEntryForDumpRequest error :: ',
                    error
                );
            }
            // pass entry_id to update same record
            let entryId;
            try {
                const parsedResp = JSON.parse(dumpResp.resp);
                entryId = parsedResp?.entry_id;
                if (entryId) {
                    jobData.dumps_entry_id = entryId;
                }
            } catch {
                console.log(
                    'attendance_model :: exportCapacityWiseAvailabilityByEmail :: createEntryForDumpRequest error :: ',
                    error
                );
            }

            allQueues.WIFY_CAPACITY_WISE_AVAILABILITY_EXPORT_BY_EMAIL.addJob(
                jobData
            );

            dumpExportReqCounter.inc({
                module: moduleKeys.capacityWiseAvailability,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processCapacityWiseAvailabilityExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                let requester = jobData.requester;

                const dbObj = this.databaseDump || this.db;
                if (!dbObj) {
                    resolve(
                        new sampleOperationResp(
                            false,
                            'DB not found',
                            HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                        )
                    );
                    return;
                }

                dbObj
                    .tms_get_capacity_wise_availability(
                        requester,
                        1,
                        999999,
                        filters_,
                        ''
                    )
                    .then(
                        (res) => {
                            var dbResp = new db_resp(
                                res[0].tms_get_capacity_wise_availability
                            );
                            if (!dbResp.status) {
                                dumpExportFailureCounter.inc({
                                    module: moduleKeys.capacityWiseAvailability,
                                });
                                dumpExportsCounter.inc({
                                    status: dumpExportStatus.failure,
                                });
                                resolve(
                                    new sampleOperationResp(
                                        false,
                                        'Internal server Error',
                                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                    )
                                );
                                return;
                            } else {
                                const stream = getStreamFromVariable(
                                    dbResp.data
                                );
                                const filePath = path.join(
                                    __dirname,
                                    '../../../',
                                    'tmp',
                                    `capacity_wise_availability_${Date.now()}.csv`
                                );
                                const jsonToCsv = require('json-to-csv-stream');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(jsonToCsv())
                                    .pipe(fs.createWriteStream(filePath))
                                    .on('finish', () => {
                                        console.log(
                                            'CSV file created successfully'
                                        );
                                        // Send email with attachment
                                        const nodemailer = require('nodemailer');
                                        // Email logic here...
                                        dumpExportSuccessCounter.inc({
                                            module: moduleKeys.capacityWiseAvailability,
                                        });
                                        dumpExportsCounter.inc({
                                            status: dumpExportStatus.success,
                                        });
                                        resolve(
                                            new sampleOperationResp(
                                                true,
                                                'success',
                                                HttpStatus.StatusCodes.OK
                                            )
                                        );
                                    });
                            }
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.capacityWiseAvailability,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                console.log(
                    'attendance_model :: processCapacityWiseAvailabilityExportByEmail :: error :: ',
                    error
                );
                dumpExportFailureCounter.inc({
                    module: moduleKeys.capacityWiseAvailability,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                resolve(
                    new sampleOperationResp(
                        false,
                        'Internal server Error',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
            }
        });
    }

    getAvailabilitySingleEntry(query, entry_id) {
        return new Promise((resolve, reject) => {
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_availability_details(entry_id).then(
                (res) => {
                    if (!res || !res[0]) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Unknown error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    }
                    var dbResp = new db_resp(
                        res[0].tms_get_availability_details
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        this.getViewDataFrAvailabilityForm({}).then(
                            (operationResp) => {
                                if (operationResp.isSuccess()) {
                                    var finalResp = JSON.parse(
                                        operationResp.resp
                                    );
                                    finalResp.form_data = dbResp.data;
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            JSON.stringify(finalResp),
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                } else {
                                    resolve(operationResp);
                                }
                            }
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    exportAvailabilityByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const jobData = { requester, filters_ };
            allQueues.WIFY_AVAILABILITY_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({
                module: moduleKeys.availability,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processAvailabilityExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                dbObj
                    .tms_get_availability_req_dump(requesterInfo, filters_, {
                        stream: true,
                    })
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today, now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'availability_request_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `Leave Attendance Requests dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.availability,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_availability_req_dump',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                                // stream.pipe(JSONStream.stringify()).pipe(jsonToCsv()).pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.availability,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, error);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.availability,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    //Assignment
    exportAssignmentByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_addr'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const jobData = { requester, filters_ };
            allQueues.WIFY_ASSIGNMENT_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({
                module: moduleKeys.technicianAssignment,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processAssignmentExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }

                dbObj
                    .tms_get_assignment_req_dumps_fr_usr(
                        requesterInfo,
                        filters_,
                        { stream: true }
                    )
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'assignment_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `Assignment dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.technicianAssignment,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_assignment_req_dumps_fr_usr',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.technicianAssignment,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.technicianAssignment,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    getAssignmentOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_assignment_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_assignment_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAllAssignment(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;

        return new Promise((resolve, reject) => {
            //added new parameter

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_assignment(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(res[0].tms_get_assignment);
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    //Site Attendance
    getAttendanceOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_attendance_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_attendance_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAllAttendance(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;
        requester['retrieve_count'] = query.retrieve_count || false;
        return new Promise((resolve, reject) => {
            //added new parameter
            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_attendance_v2(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(res[0].tms_get_attendance_v2);
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    //Office Attendance
    getOfficeAttendanceOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_office_attendance_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_office_attendance_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    getAllOfficeAttendance(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;

        return new Promise((resolve, reject) => {
            //added new parameter

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_office_attendance(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_office_attendance
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    exportOfficeAttendanceByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const jobData = { requester, filters_ };
            allQueues.WIFY_OFFICE_ATTENDANCE_EXPORT_BY_EMAIL.addJob(jobData);

            dumpExportReqCounter.inc({
                module: moduleKeys.officeAttendance,
            });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });

            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processOfficeAttendanceExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                dbObj
                    .tms_get_office_attendance_req_dumps_fr_usr(
                        requesterInfo,
                        filters_,
                        { stream: true }
                    )
                    .then(
                        (stream) => {
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // we need to start streaming the incoming data
                            // and save to temp folder
                            // once saved trigger email
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'attendance_request_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `Office Attendance Requests dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.officeAttendance,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_office_attendance_req_dumps_fr_usr',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.officeAttendance,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.officeAttendance,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    getAllAbsentUsersEmailId(query) {
        return new Promise((resolve, reject) => {
            // query['pagination'] = '{}'; // dummy
            // var {filters_} = pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;
            requester['filters'] = query.filters;
            requester['email'] = query.email;

            const jobData = { requester };
            allQueues.WIFY_ABSENT_USER_NOTIFY_BY_EMAIL.addJob(jobData);

            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processAbsentUserNotifyByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbReplica || this.db;
                if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }

                dbObj
                    .tms_get_absent_user_email_ids(requesterInfo, filters_)
                    .then((res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_absent_user_email_ids
                        );

                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            if (dbResp?.data?.length > 0) {
                                const emailIdsPerChunk = 49; // email per chunk

                                const emailArray = dbResp?.data;
                                const finalEmails = emailArray.reduce(
                                    (resultArray, item, index) => {
                                        const chunkIndex = Math.floor(
                                            index / emailIdsPerChunk
                                        );
                                        if (!resultArray[chunkIndex]) {
                                            resultArray[chunkIndex] = []; // start a new chunk
                                        }
                                        resultArray[chunkIndex].push(item);
                                        return resultArray;
                                    },
                                    []
                                );

                                if (finalEmails) {
                                    finalEmails.forEach((singleFinalEmail) => {
                                        let emailIds =
                                            singleFinalEmail.toString();
                                        this.sendEmail(emailIds, jobData);
                                    });
                                }
                            }

                            resolve(
                                new sampleOperationResp(
                                    true,
                                    'Added to email queue',
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                        (err) => {
                            this.fatalDbError(resolve, err);
                        };
                    });
            } catch (error) {
                this.fatalDbError(resolve, error);
            }
        });
    }

    sendEmail(emailIds, jobData) {
        let org_id = jobData?.requester?.org_id;
        const d = new Date(); // today now
        let today = d.toLocaleDateString(); // YYYY-MM-DD
        const linkUrl = process.env.FRONTEND_URL;

        //Send email by QUEUE
        let to = jobData?.requester?.email;
        let subject = `Punch-In Reminder for ${today}`;
        let message = `Hi , <br><br> You have not punched in your attendance today. Login to ${linkUrl}`;
        let bcc = emailIds;

        //optinal param for save eamil_log
        let usr_id = jobData?.requester?.usr_id;
        let ip_address = jobData?.requester?.ip_addr;
        let user_agent = jobData?.requester?.user_agent;

        const emailJobData = {
            to,
            bcc,
            subject,
            message,
            org_id,
            usr_id,
            ip_address,
            user_agent,
        };
        allQueues.WIFY_SEND_EMAIL.addJob(emailJobData);
    }

    //Daily report
    getDailyreport(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;
        requester['retrieve_count'] = query.retrieve_count || false;

        return new Promise((resolve, reject) => {
            //added new parameter

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_daily_report_of_on_field_user_v2(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_daily_report_of_on_field_user_v2
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getDailyReportOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.dbReplica || this.db;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_attendance_report_overview_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_attendance_report_overview_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    exportDailyReportAttendanceByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const jobData = { requester, filters_ };
            allQueues.WIFY_DAILY_REPORT_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({ module: moduleKeys.dailyReport });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processDailyReportExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                dbObj
                    .tms_get_daily_report_onfield_dumps(
                        requesterInfo,
                        filters_,
                        { stream: true }
                    )
                    .then(
                        (stream) => {
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'attendance_request_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }

                                let fileName = `Daily Report Requests dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.dailyReport,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_daily_report_onfield_dumps',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.dailyReport,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.dailyReport,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    //Range report
    getRangeReport(query) {
        var { page_no_, page_size_, search_query, filters_ } =
            pagination_filters_utils.decodeQueryParams(query);
        var requester = {};
        requester['org_id'] = users_model.getOrgId(this.userContext);
        requester['usr_id'] = users_model.getUUID(this.userContext);
        requester['ip_address'] = this.ip_address;
        requester['user_agent'] = this.user_agent_;

        return new Promise((resolve, reject) => {
            //added new parameter

            const dbObj = this.db || this.dbReplica;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj
                .tms_get_range_report_of_onfield(
                    requester,
                    page_no_,
                    page_size_,
                    filters_,
                    search_query
                )
                .then(
                    (res) => {
                        var dbResp = new db_resp(
                            res[0].tms_get_range_report_of_onfield
                        );
                        if (!dbResp.status) {
                            resolve(
                                new sampleOperationResp(
                                    false,
                                    'Internal server Error',
                                    HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                                )
                            );
                            return;
                        } else {
                            resolve(
                                new sampleOperationResp(
                                    true,
                                    JSON.stringify(dbResp.data),
                                    HttpStatus.StatusCodes.OK
                                )
                            );
                        }
                    },
                    (error) => {
                        this.fatalDbError(resolve, error);
                    }
                );
        });
    }

    getRangeReportOverviewProto(query) {
        return new Promise((resolve, reject) => {
            //added new parameter
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            query['org_id'] = users_model.getOrgId(this.userContext);
            query['usr_id'] = users_model.getUUID(this.userContext);
            query['ip_address'] = this.ip_address;
            query['user_agent'] = this.user_agent_;
            query['filters'] = filters_;
            var form_data = JSON.stringify(query);

            const dbObj = this.db || this.dbReplica;
            if (this.dbReplica) {
                console.log('Loading data from Replica');
            }
            if (!dbObj) {
                resolve(
                    new sampleOperationResp(
                        false,
                        'DB not found',
                        HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                    )
                );
                return;
            }
            dbObj.tms_get_range_report_overvivew_proto(form_data).then(
                (res) => {
                    var dbResp = new db_resp(
                        res[0].tms_get_range_report_overvivew_proto
                    );

                    if (!dbResp.status) {
                        resolve(
                            new sampleOperationResp(
                                false,
                                'Internal server Error',
                                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
                            )
                        );
                        return;
                    } else {
                        resolve(
                            new sampleOperationResp(
                                true,
                                JSON.stringify(dbResp.data),
                                HttpStatus.StatusCodes.OK
                            )
                        );
                    }
                },
                (error) => {
                    this.fatalDbError(resolve, error);
                }
            );
        });
    }

    exportRangeReportAttendanceByEmail(query) {
        return new Promise((resolve, reject) => {
            query['pagination'] = '{}'; // dummy
            var { filters_ } =
                pagination_filters_utils.decodeQueryParams(query);
            var requester = query;
            requester['org_id'] = users_model.getOrgId(this.userContext);
            requester['usr_id'] = users_model.getUUID(this.userContext);
            requester['ip_address'] = this.ip_address;
            requester['user_agent'] = this.user_agent_;

            const jobData = { requester, filters_ };
            allQueues.WIFY_RANGE_REPORT_EXPORT_BY_EMAIL.addJob(jobData);
            dumpExportReqCounter.inc({ module: moduleKeys.rangeReport });
            dumpExportsCounter.inc({
                status: dumpExportStatus.requested,
            });
            resolve(
                new sampleOperationResp(
                    true,
                    'success',
                    HttpStatus.StatusCodes.OK
                )
            );
        });
    }

    processRangeReportExportByEmail(jobData) {
        return new Promise((resolve, reject) => {
            try {
                let requesterInfo = JSON.stringify(jobData.requester);
                let filters_ = jobData.filters_;
                const dbObj = this.dbDump || this.dbReplica || this.db;
                if (this.dbDump) {
                    console.log('Loading data from Dump');
                } else if (this.dbReplica) {
                    console.log('Loading data from Replica');
                }
                dbObj
                    .tms_get_range_report_dump(requesterInfo, filters_, {
                        stream: true,
                    })
                    .then(
                        (stream) => {
                            let org_id = jobData?.requester?.org_id;
                            const d = new Date(); // today now
                            let today = d.toISOString().slice(0, 10); // YYYY-MM-DD
                            let savePath = path.join(
                                '',
                                'temp_files',
                                'attendance_request_dump',
                                '' + org_id,
                                today
                            );
                            fs.mkdir(savePath, { recursive: true }, (err) => {
                                if (err) {
                                    if (err.code != 'EEXIST') {
                                        return console.log(
                                            'Error in temp folder creation',
                                            err
                                        );
                                    }
                                }
                                console.log('request', requesterInfo);
                                let fileName = `Range Report Requests dump ${today}_${d.getTime()}.csv`;
                                let filePath = path.join(savePath, fileName);
                                stream.on('end', () => {
                                    // do something with the created file
                                    console.log('Streaming ended -----');

                                    //Send email by QUEUE
                                    let to = jobData.requester?.email_id;
                                    let subject = jobData.requester?.subject;
                                    let message =
                                        '------System generated report as requested on <a href="http://tms.wify.co.in">TMS</a>------';
                                    let attachments = [
                                        { path: filePath, filename: fileName },
                                    ];

                                    //optinal param for save eamil_log
                                    let usr_id = jobData?.requester?.usr_id;
                                    let ip_address =
                                        jobData?.requester?.ip_addr;
                                    let user_agent =
                                        jobData?.requester?.user_agent;

                                    const emailJobData = {
                                        to,
                                        subject,
                                        message,
                                        attachments,
                                        org_id,
                                        usr_id,
                                        ip_address,
                                        user_agent,
                                    };
                                    allQueues.WIFY_SEND_EMAIL.addJob(
                                        emailJobData
                                    );
                                    dumpExportSuccessCounter.inc({
                                        module: moduleKeys.rangeReport,
                                    });
                                    dumpExportsCounter.inc({
                                        status: dumpExportStatus.success,
                                    });
                                    resolve(
                                        new sampleOperationResp(
                                            true,
                                            'Added to email queue',
                                            HttpStatus.StatusCodes.OK
                                        )
                                    );
                                });

                                console.log('Streaming started');
                                stream
                                    .pipe(JSONStream.stringify())
                                    .pipe(
                                        jsonToCsv({
                                            path: '*.tms_get_range_report_dump',
                                        })
                                    )
                                    .pipe(fs.createWriteStream(filePath));
                            });
                        },
                        (err) => {
                            dumpExportFailureCounter.inc({
                                module: moduleKeys.rangeReport,
                            });
                            dumpExportsCounter.inc({
                                status: dumpExportStatus.failure,
                            });
                            this.fatalDbError(resolve, err);
                        }
                    );
            } catch (error) {
                dumpExportFailureCounter.inc({
                    module: moduleKeys.rangeReport,
                });
                dumpExportsCounter.inc({
                    status: dumpExportStatus.failure,
                });
                this.fatalDbError(resolve, error);
            }
        });
    }

    //write model same as for avavailablity for capacity-wise-avavilablity
    fatalDbError(resolve, error) {
        // This is db level error need to be captured
        // mandatorily include this
        resolve(
            new sampleOperationResp(
                false,
                error,
                HttpStatus.StatusCodes.INTERNAL_SERVER_ERROR
            )
        );
    }

    validateAttendanceForm(form_data) {
        return true;
    }

    getAttendanceModelObj(model_data) {
        return {
            ip_address: model_data?.ip_address,
            leave_type: model_data?.leave_type,
            org_id: model_data?.org_id,
            remarks: model_data?.remarks,
            role_id: model_data?.role_id,
            single_day: model_data?.single_day,
            technician_id: model_data?.technician_id,
            user_agent: model_data?.user_agent,
            user_id: model_data?.user_id,
            is_system_generated_leave: model_data?.is_system_generated_leave,
            single_dat: model_data.single_day,
        };
    }

    set ip_addr(ip_address) {
        this.ip_address = ip_address;
    }
    set user_agent(user_agent_) {
        this.user_agent_ = user_agent_;
    }

    set database(db) {
        this.db = db;
    }

    get database() {
        return this.db;
    }

    set databaseReplica(db) {
        this.dbReplica = db;
    }

    get databaseReplica() {
        return this.dbReplica;
    }

    set databaseDump(db) {
        this.dbDump = db;
    }

    get databaseDump() {
        return this.dbDump;
    }

    set user_context(userContext) {
        this.userContext = userContext;
    }

    get user_context() {
        return this.userContext;
    }
    getInstance() {
        const instance = new attendance_model();
        return instance;
    }

    getFreshInstance(model) {
        const clonedInstance = new attendance_model();
        Object.assign(clonedInstance, model);
        return clonedInstance;
    }
}

module.exports = new attendance_model();
