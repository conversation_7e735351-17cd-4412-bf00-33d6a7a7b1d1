import {
    Modal,
    Form,
    Input,
    Button,
    Select,
    Upload,
    Row,
    Col,
    Collapse,
    Tabs,
    DatePicker,
} from 'antd';
import React, { Component } from 'react';
import CircularProgress from '../../../components/CircularProgress';
import http_utils from '../../../util/http_utils';
import FormBuilder from 'antd-form-builder';
import moment from 'moment';
import {
    convertDateFieldsToMoments,
    getPresetRangesForRangeDatePicker,
} from '../../../util/helpers';
import ConfigHelpers from '../../../util/ConfigHelpers';

const protoUrl = '/attendance/availability/proto';
const submitUrl = '/attendance/availability';

class LeaveEditor extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
    }

    state = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        error: '',
        fileList: [],
        editMode: this.props.editMode,
        editModeForceRefreshDone: false,
    };

    componentDidMount() {
        this.initViewData();
    }

    initViewData() {
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {};
            const onComplete = (resp) => {
                // console.log("resp",resp);
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };

            var url = !this.state.editMode
                ? protoUrl
                : protoUrl + '/' + this.props.editorItem.attendance_id;

            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                        this.setState({
                            editModeForceRefreshDone: false,
                        });
                    }
                }
            );
        }
    }

    handleFileChanged(data) {
        this.submitForm(data);
    }

    refresh() {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    }

    handleOk = () => {
        this.setState({
            ModalText: 'The modal will be closed after two seconds',
            isFormSubmitting: true,
        });
        setTimeout(() => {
            this.setState({
                visible: false,
                isFormSubmitting: false,
            });
            this.updateClosureToParent();
        }, 2000);
    };

    tellParentToRefreshList(entry_id) {
        console.log('Trying to to tell parent to refresh list');
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
    }

    handleCancel = () => {
        this.setState({
            visible: false,
        });
        this.updateClosureToParent();
    };

    submitForm = (data) => {
        this.setState({
            isFormSubmitting: true,
        });
        var params = data;
        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: false,
            });
            this.tellParentToRefreshList(resp.entry_id);
            this.updateClosureToParent();
        };
        const onError = (error) => {
            console.log('Got error', error);
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: error.response.data,
            });
        };
        if (this.state.editMode) {
            http_utils.performPutCall(
                submitUrl + '/' + this.props.editorItem.attendance_id,
                params,
                onComplete,
                onError
            );
        } else {
            http_utils.performPostCall(submitUrl, params, onComplete, onError);
        }
    };

    getMeta = () => {
        if (this.state.editMode && !this.state.editModeForceRefreshDone) {
            // refreshing state to get form ref
            this.setState({ editModeForceRefreshDone: true });
            return;
        }

        let technician_list = [];
        let role_wise_technician = this.state.viewData?.role_wise_technician;
        let selected_role_id = this.formRef?.current?.getFieldValue('role_id');
        if (selected_role_id) {
            let technician_id =
                this.formRef?.current?.getFieldValue('technician_id');
            if (technician_id == undefined) {
                this.formRef.current.setFieldsValue({ technician_id: [] });
            }
            technician_list = role_wise_technician?.filter(
                (item) => item.value == selected_role_id
            )[0]?.technician_details;
        }

        let attendanceSpanFieldMeta = [];
        let selected_leave_type =
            this.formRef?.current?.getFieldValue('leave_type');
        if (selected_leave_type == 'single_day') {
            attendanceSpanFieldMeta.push({
                key: `${selected_leave_type}`,
                widget: 'date-picker',
                widgetProps: {
                    style: {
                        width: '100%',
                    },
                    onChange: (value, dateString) => {
                        this.formRef.current.setFieldsValue({
                            single_day: moment.utc(dateString),
                        });
                    },
                },
                rules: [
                    {
                        required: true,
                        message: 'Please select date',
                    },
                ],
            });
        } else if (selected_leave_type == 'range') {
            attendanceSpanFieldMeta.push({
                key: `${selected_leave_type}`,
                widget: DatePicker.RangePicker,
                widgetProps: {
                    ranges: getPresetRangesForRangeDatePicker(),
                    style: {
                        width: '100%',
                    },
                    onChange: (value, dateString) => {
                        // this.formRef.current.setFieldsValue({ range: moment.utc(dateString) });
                    },
                },
                rules: [
                    {
                        required: true,
                        message: 'Please select date range',
                    },
                ],
            });
        } else if (selected_leave_type == 'half_day') {
            attendanceSpanFieldMeta.push(
                {
                    key: `${selected_leave_type}`,
                    label: 'Select Date and Time',
                    widget: 'date-picker',
                    widgetProps: {
                        style: {
                            width: '100%',
                        },
                        onChange: (value, dateString) => {
                            this.formRef.current.setFieldsValue({
                                half_day: moment.utc(dateString),
                            });
                        },
                    },
                    rules: [
                        {
                            required: true,
                            message: 'Please select date',
                        },
                    ],
                },
                {
                    key: `${selected_leave_type}_type`,
                    widget: 'radio-group',
                    forwardRef: true,
                    options: [
                        {
                            label: 'First Half',
                            value: 'first_half',
                        },
                        {
                            label: 'Second Half',
                            value: 'second_half',
                        },
                    ],
                    rules: [
                        {
                            required: true,
                            message: 'Please select half day type',
                        },
                    ],
                }
            );
        }

        let attendanceMeta = [];
        if (!this.state.editMode) {
            let userMeta = [];
            if (!this.props.isAccessMyLeave) {
                userMeta.push(
                    {
                        key: 'role_id',
                        label: 'Select roles',
                        widget: 'select',
                        widgetProps: {
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        },
                        rules: [
                            {
                                required: true,
                                message: 'Please select role',
                            },
                        ],
                        options: role_wise_technician,
                        onChange: () => this.refresh(),
                    },
                    {
                        key: 'technician_id',
                        label: 'Select technician name',
                        widget: 'select',
                        widgetProps: {
                            allowClear: true,
                            showSearch: true,
                            optionFilterProp: 'children',
                        },
                        rules: [
                            {
                                required: true,
                                message: 'Please select technician',
                            },
                        ],
                        options: technician_list,
                    }
                );
            }

            attendanceMeta.push(
                ...userMeta,
                {
                    key: 'leave_type',
                    label: 'Select Span',
                    widget: 'radio-group',
                    options: [
                        {
                            label: 'Single Day',
                            value: 'single_day',
                        },
                        {
                            label: 'Range',
                            value: 'range',
                        },
                        {
                            label: 'Half Day',
                            value: 'half_day',
                        },
                    ],
                    rules: [
                        {
                            required: true,
                            message: 'Please select span',
                        },
                    ],
                    onChange: () => this.refresh(),
                },
                ...attendanceSpanFieldMeta,
                {
                    key: 'remarks',
                    label: 'Remarks',
                    widget: 'textarea',
                }
            );
        } else {
            attendanceMeta.push({
                key: 'status',
                label: 'Select Status ( Pending /Approved / Rejected )',
                widget: 'select',
                widgetProps: {
                    allowClear: true,
                    showSearch: true,
                    optionFilterProp: 'children',
                },
                rules: [
                    {
                        required: true,
                        message: 'Please select status',
                    },
                ],
                options: ['Pending', 'Approved', 'Rejected'],
            });
        }

        const meta = {
            columns: 1,
            formItemLayout: null,
            fields: [...attendanceMeta],
        };
        return meta;
    };

    render() {
        const { editorItem } = this.props;
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
        } = this.state;
        var editorTitle = editorItem?.technician_name;
        var editMode = true;
        if (editorTitle == undefined) {
            editorTitle = 'Create new Leave';
            editMode = false;
        }

        let prefillFormData = this.state.viewData?.form_data;
        if (prefillFormData) {
            prefillFormData = convertDateFieldsToMoments(
                this.state.viewData?.form_data,
                this.getMeta()?.fields
            );
        }

        return visible ? (
            <Modal
                title={`${editorTitle}`}
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                onCancel={this.handleCancel}
                footer={null}
            >
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <Row>
                        <Col xs={24}>
                            <Form
                                className="ant-col gx-my-1 ant-col-xs-24 gx-mt-3"
                                layout="vertical"
                                ref={this.formRef}
                                onFinish={(data) => {
                                    this.submitForm(data);
                                }}
                                initialValues={
                                    this.state.editMode ? prefillFormData : {}
                                }
                            >
                                <FormBuilder
                                    meta={this.getMeta()}
                                    form={this.formRef}
                                />

                                <Form.Item>
                                    <Button
                                        type="primary"
                                        htmlType="submit"
                                        disabled={isFormSubmitting}
                                    >
                                        {editMode ? 'Save' : 'Submit'}
                                    </Button>
                                </Form.Item>
                                {isFormSubmitting ? (
                                    <div className="gx-loader-view gx-loader-position">
                                        <CircularProgress />
                                    </div>
                                ) : null}
                                {error ? (
                                    <p className="gx-text-red">{error}</p>
                                ) : null}
                            </Form>
                        </Col>
                    </Row>
                )}
            </Modal>
        ) : (
            <></>
        );
    }
}

export default LeaveEditor;
