import React from 'react';
import { render } from '@testing-library/react';
import AutoAssignWidget from './AutoAssignWidget';
beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});
jest.mock('react-countup', () => ({
    __esModule: true,
    default: ({ end }) => <span>{end}</span>,
}));

describe('Smoke Test for AutoAssignWidget', () => {
    it('renders without crashing', () => {
        render(<AutoAssignWidget />);
    });
});
