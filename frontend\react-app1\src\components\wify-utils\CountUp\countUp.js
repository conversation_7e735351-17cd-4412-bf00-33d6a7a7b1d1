// CountUp.js
import React, { useEffect, useRef, useState } from 'react';

const easeOutQuad = (t) => t * (2 - t);

function CountUp({
    start = 0,
    end,
    duration = 2,
    decimals = 0,
    prefix = '',
    suffix = '',
    onEnd = () => {},
}) {
    const [current, setCurrent] = useState(start);
    const startTimeRef = useRef(null);
    const rafIdRef = useRef(null);

    useEffect(() => {
        const startTime = performance.now();
        startTimeRef.current = startTime;

        const animate = (time) => {
            const elapsed = (time - startTime) / 1000; // seconds
            const progress = Math.min(elapsed / duration, 1);
            const easedProgress = easeOutQuad(progress);
            const value = start + (end - start) * easedProgress;

            setCurrent(parseFloat(value.toFixed(decimals)));

            if (progress < 1) {
                rafIdRef.current = requestAnimationFrame(animate);
            } else {
                onEnd();
            }
        };

        rafIdRef.current = requestAnimationFrame(animate);

        return () => cancelAnimationFrame(rafIdRef.current);
    }, [start, end, duration]);

    return (
        <span>
            {prefix}
            {current.toLocaleString(undefined, {
                minimumFractionDigits: decimals,
                maximumFractionDigits: decimals,
            })}
            {suffix}
        </span>
    );
}

export default CountUp;
