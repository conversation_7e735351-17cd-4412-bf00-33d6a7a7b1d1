import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons';
import { Button, Collapse, Modal } from 'antd';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { decodeFieldsMetaFrmJson } from '../../components/wify-utils/FieldCreator/helpers';
import MetaInputTable from '../../components/wify-utils/MetaInputTable';
import {
    convertDateFieldsToMoments,
    convertMomentToLocalDateString,
    convertUTCToDisplayTime,
    downloadExcelFileWithTabwise,
    getValueDataFrmFormMeta,
    parseFormulaToValue,
} from '../../util/helpers';
import _, { forEach } from 'lodash';
import LineItemConfigSummary from '../../components/WIFY/LineItemConfigSummary';
import ConfigHelpers from '../../util/ConfigHelpers';
import CountUp from '../../components/wify-utils/CountUp/countUp';

const numOr0 = (n) => (!n || isNaN(n) ? 0 : n);

const Revisions = ({ revisionsObj, onRestoreClick, config, readOnly }) => {
    const [showRevisions, setShowRevisions] = useState(false);
    return (
        <span>
            <Button
                onClick={() => setShowRevisions(true)}
                icon={<ReloadOutlined />}
                size="small"
            >
                Revisions
            </Button>
            {showRevisions && (
                <Modal
                    width={1200}
                    title="Revision history"
                    visible
                    onOk={() => {
                        setShowRevisions(false);
                    }}
                    onCancel={() => {
                        setShowRevisions(false);
                    }}
                >
                    <Collapse>
                        {revisionsObj
                            .slice(0)
                            .reverse()
                            .map((singleRevision, index) => {
                                return (
                                    <Collapse.Panel
                                        header={`${convertUTCToDisplayTime(singleRevision.c_time)} - ${singleRevision.c_name}`}
                                        key={index}
                                    >
                                        <div>
                                            <Button
                                                icon={<ReloadOutlined />}
                                                onClick={() => {
                                                    onRestoreClick(
                                                        singleRevision
                                                    );
                                                    setShowRevisions(false);
                                                }}
                                                disabled={readOnly}
                                            >
                                                Restore
                                            </Button>
                                        </div>
                                        <LineItems
                                            config={config}
                                            lineItemsData={{
                                                form_data:
                                                    singleRevision?.form_data,
                                            }}
                                            readOnly
                                        />
                                    </Collapse.Panel>
                                );
                            })}
                    </Collapse>
                </Modal>
            )}
        </span>
    );
};

const LineItems = ({
    config,
    lineItemsData,
    onChange,
    readOnly,
    srvcConfigData,
    isBrand,
    isSrvcReqLock = false,
    srvc_type_id,
    revisionsData,
    title,
    heading,
    downloadLineItemData,
    allow_sp_line_items_download,
    isCustAccess,
}) => {
    const [reRenderLineItems, setReRenderLineItems] = useState(false);
    const form_data = lineItemsData?.form_data || {};
    const lineItemDataFrDownload = downloadLineItemData?.form_data || {};
    let revisions = lineItemsData?.revisions || [];
    if (revisionsData && revisionsData?.length > 0) {
        revisions = revisionsData;
    }
    const [renderHelper, setRenderHelper] = useState(false);
    // console.log('config',config);
    useEffect(() => {
        setRenderHelper(!renderHelper);
    }, [lineItemsData]);

    const getColMeta = (lineItemGroup) => {
        const fields = decodeFieldsMetaFrmJson(lineItemGroup.fields) || [];
        const configFrGrp = config[lineItemGroup.key];
        const { quantity_field_formula } = configFrGrp;
        let lineItemMstPriceFromSrvcConfig = getLineItemMstPriceFormSrvcConfig(
            lineItemGroup.key,
            srvcConfigData
        );
        const staticColumns = [
            {
                key: 'qty',
                label: lineItemGroup.quantity_field_label || 'Quantity',
                widget: 'number',
                ...(quantity_field_formula?.length > 0
                    ? {
                          viewMode: true,
                          renderView: (text) => {
                              return (
                                  <div className="gx-px-2 gx-text-orange">
                                      <b>
                                          <CountUp
                                              end={text}
                                              duration={0.5}
                                              decimal="."
                                              decimals={2}
                                          />
                                      </b>
                                  </div>
                              );
                          },
                      }
                    : {}),
            },
            {
                key: 'rate',
                label: lineItemGroup.price_field_label || 'Price',
                widget: 'number',
                widgetProps: {
                    disabled:
                        lineItemMstPriceFromSrvcConfig &&
                        lineItemMstPriceFromSrvcConfig != '',
                },
            },
            {
                key: 'total',
                label: 'Total',
                widget: 'number',
                viewMode: true,
                renderView: (text) => {
                    return (
                        <div className="gx-px-2 gx-text-orange">
                            <b>
                                <CountUp
                                    end={text}
                                    duration={0.5}
                                    {...countUpParams}
                                />
                            </b>
                        </div>
                    );
                },
            },
        ];
        return [...fields, ...staticColumns];
    };
    const getSpSrvcConfigData = () => {
        return srvcConfigData || [];
    };
    const getSpPriceConfigForSrvcTypeId = (
        srvcTypePriceConfig,
        lineItemGroupKey
    ) => {
        let prcConfigDatafrSingleSrvcType = [];
        let priceConfigFrSingleSrvcTypeId =
            srvcTypePriceConfig[lineItemGroupKey];
        if (
            priceConfigFrSingleSrvcTypeId &&
            priceConfigFrSingleSrvcTypeId.length > 0
        ) {
            prcConfigDatafrSingleSrvcType =
                priceConfigFrSingleSrvcTypeId.filter(
                    (singlePrcFrSrvcTypeId) =>
                        singlePrcFrSrvcTypeId.srvc_type_id == srvc_type_id
                )?.[0];
        } else {
            prcConfigDatafrSingleSrvcType = priceConfigFrSingleSrvcTypeId;
        }
        return prcConfigDatafrSingleSrvcType;
    };

    const getLineItemMstPriceFormSrvcConfig = (
        lineItemGroupKey,
        srvcConfigData
    ) => {
        let lineItemMasterPrice = '';
        let srvcTypePriceConfig = !isBrand
            ? getSpSrvcConfigData()?.srvc_type_pricing_config_for_line_item
            : srvcConfigData?.srvc_type_pricing_config_for_line_item;
        srvcTypePriceConfig =
            srvcTypePriceConfig ||
            srvcConfigData?.srvc_type_pricing_config_for_line_item;
        if (srvcTypePriceConfig) {
            srvcTypePriceConfig = JSON.parse(srvcTypePriceConfig);
            let lineItemPriceConfigFrGrp = !isBrand
                ? getSpPriceConfigForSrvcTypeId(
                      srvcTypePriceConfig,
                      lineItemGroupKey
                  )
                : srvcTypePriceConfig[lineItemGroupKey];
            if (
                lineItemPriceConfigFrGrp &&
                lineItemPriceConfigFrGrp.length > 0
            ) {
                let lineItemPriceConfigkey =
                    'line_item_' + lineItemGroupKey + '_master_rate';
                lineItemMasterPrice =
                    lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
            } else {
                if (isBrand) {
                    let lineItemPriceConfigFrGrp =
                        srvcTypePriceConfig[lineItemGroupKey];
                    let lineItemPriceConfigkey =
                        'line_item_' + lineItemGroupKey + '_master_rate';
                    lineItemMasterPrice =
                        lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
                } else {
                    let lineItemPriceConfigkey =
                        'line_item_' + lineItemGroupKey + '_master_rate';
                    lineItemMasterPrice =
                        lineItemPriceConfigFrGrp?.[lineItemPriceConfigkey];
                }
            }
        }
        return lineItemMasterPrice;
    };

    const getLineItemSelectPriceFormSrvcConfig = (
        lineItemGroup,
        singleLineItem
    ) => {
        const fields = decodeFieldsMetaFrmJson(lineItemGroup?.fields) || [];
        let lineItemSelectPrice = '';
        let priceList = [];
        let masterPrice = getLineItemMstPriceFormSrvcConfig(
            lineItemGroup?.key,
            srvcConfigData
        );
        if (masterPrice) {
            priceList.push(masterPrice);
        }
        let srvcTypePriceConfig = !isBrand
            ? getSpSrvcConfigData()?.srvc_type_pricing_config_for_line_item
            : srvcConfigData?.srvc_type_pricing_config_for_line_item;

        if (srvcTypePriceConfig && fields && fields.length > 0) {
            fields.forEach((singleField) => {
                let selectedSelectKey = singleLineItem[singleField.key];
                if (selectedSelectKey) {
                    if (typeof srvcTypePriceConfig != 'object') {
                        srvcTypePriceConfig = JSON.parse(srvcTypePriceConfig);
                    }
                    let lineItemPriceConfigFrGrp = !isBrand
                        ? getSpPriceConfigForSrvcTypeId(
                              srvcTypePriceConfig,
                              lineItemGroup?.key
                          )
                        : srvcTypePriceConfig[lineItemGroup?.key];

                    lineItemSelectPrice =
                        lineItemPriceConfigFrGrp?.[selectedSelectKey];
                    if (lineItemSelectPrice) {
                        priceList.push(lineItemSelectPrice);
                    }
                }
            });
        }
        // console.log("priceList",priceList)
        lineItemSelectPrice = getPriceBasedOnDeterminationEngineRule(
            lineItemGroup,
            priceList
        );
        return lineItemSelectPrice;
    };

    const getPriceBasedOnDeterminationEngineRule = (
        lineItemGroup,
        priceList
    ) => {
        let PriceBaseOnDeterminationEngineRule = '';
        let prc_config_determination_engine_key =
            'srvc_type_' +
            lineItemGroup?.key +
            '_pricing_config_determination_engine';
        if (srvcConfigData) {
            let prc_config_determination_engine_rule = !isBrand
                ? getSpSrvcConfigData()?.[prc_config_determination_engine_key]
                : srvcConfigData[prc_config_determination_engine_key];

            if (priceList && priceList.length > 0 && priceList != '') {
                if (prc_config_determination_engine_rule == 'lowest') {
                    PriceBaseOnDeterminationEngineRule = priceList?.reduce(
                        (singlepriceList, value) =>
                            Math.min(singlepriceList, value)
                    );
                } else if (prc_config_determination_engine_rule == 'highest') {
                    PriceBaseOnDeterminationEngineRule = priceList?.reduce(
                        (singlepriceList, value) =>
                            Math.max(singlepriceList, value)
                    );
                } else if (
                    prc_config_determination_engine_rule == 'aggregate'
                ) {
                    let total = priceList?.reduce(function (
                        singlepriceList,
                        value
                    ) {
                        return singlepriceList + value;
                    }, 0);
                    PriceBaseOnDeterminationEngineRule =
                        (total / priceList.length)?.toFixed(2) || 0;
                }
            }
        }
        return PriceBaseOnDeterminationEngineRule;
    };

    const getIdVsLabelMapping = (fields) => {
        const idVsLabelMapping = {};
        fields.forEach((singleFieldMeta) => {
            idVsLabelMapping[singleFieldMeta.label] = singleFieldMeta.key;
        });
        return idVsLabelMapping;
    };

    const getRowData = (lineItemGroup, isDownloadLineItemData = false) => {
        let lineItemsFrGrp = isDownloadLineItemData
            ? lineItemDataFrDownload[lineItemGroup.key] || [{}]
            : form_data[lineItemGroup.key] || [{}];
        const fields = decodeFieldsMetaFrmJson(lineItemGroup.fields) || [];
        if (lineItemsFrGrp) {
            lineItemsFrGrp.forEach((singleLineItemGrp) => {
                let singleGrpData = convertDateFieldsToMoments(
                    singleLineItemGrp,
                    fields
                );
                let srvcTypeCustomPriceconfig =
                    getLineItemSelectPriceFormSrvcConfig(
                        lineItemGroup,
                        singleGrpData
                    );
                if (
                    srvcTypeCustomPriceconfig &&
                    srvcTypeCustomPriceconfig != ''
                ) {
                    singleGrpData['rate'] = srvcTypeCustomPriceconfig;
                }

                const { quantity_field_formula } = config[lineItemGroup.key];
                if (
                    quantity_field_formula &&
                    quantity_field_formula.length > 0
                ) {
                    const qty = parseFormulaToValue(
                        quantity_field_formula,
                        getIdVsLabelMapping(fields),
                        singleGrpData,
                        !isSrvcReqLock ? false : undefined
                    );

                    if (!isNaN(qty)) {
                        singleGrpData.qty = qty;
                    }
                }
                singleGrpData.total =
                    numOr0(singleGrpData.qty) * numOr0(singleGrpData.rate);
            });

            let newLineItemsData = { ...lineItemsData };

            let form_data = newLineItemsData?.form_data || {};
            newLineItemsData['form_data'] = form_data;
            newLineItemsData['total'] = getLineItemsTotal(newLineItemsData);
            newLineItemsData['total_qty'] =
                getLineItemsTotalQty(newLineItemsData);
            let finalLineItemsSingleGroupWiseTotalQtyObj =
                getLineItemsSingleGroupWiseTotalQty(
                    newLineItemsData,
                    lineItemGroup.key
                );
            newLineItemsData = {
                ...newLineItemsData,
                ...finalLineItemsSingleGroupWiseTotalQtyObj,
            };
            if (
                JSON.stringify(newLineItemsData) !==
                    JSON.stringify(lineItemsData) &&
                isSrvcReqLock == false
            ) {
                if (onChange) {
                    onChange(newLineItemsData);
                }
            }
        }
        return lineItemsFrGrp;
    };

    const setNewRowData = (lineItemGroup, newData) => {
        // console.log('config',config);
        const fields = decodeFieldsMetaFrmJson(lineItemGroup.fields) || [];
        const configFrGrp = config[lineItemGroup.key];
        // Do Quantity and total calculations
        newData.forEach((singleLineItem) => {
            //rate autoifll based on srvc_config_price.
            //if rate is not define in price_config_master then blank and editable.
            //if rate is define then disable.
            let srvcTypeCustomPriceconfig =
                getLineItemSelectPriceFormSrvcConfig(
                    lineItemGroup,
                    singleLineItem
                );
            if (srvcTypeCustomPriceconfig) {
                singleLineItem.rate = srvcTypeCustomPriceconfig;
            }
            const { quantity_field_formula } = configFrGrp;
            if (quantity_field_formula && quantity_field_formula.length > 0) {
                const qty = parseFormulaToValue(
                    quantity_field_formula,
                    getIdVsLabelMapping(fields),
                    singleLineItem,
                    false
                );
                singleLineItem.qty = qty;
            }
            singleLineItem.total =
                numOr0(singleLineItem.qty) * numOr0(singleLineItem.rate);
        });

        let newLineItemsData = { ...lineItemsData };
        let form_data = newLineItemsData?.form_data || {};
        form_data[lineItemGroup.key] = newData;
        newLineItemsData['form_data'] = form_data;
        newLineItemsData['total'] = getLineItemsTotal(newLineItemsData);
        newLineItemsData['total_qty'] = getLineItemsTotalQty(newLineItemsData);
        let finalLineItemsSingleGroupWiseTotalQtyObj =
            getLineItemsSingleGroupWiseTotalQty(
                newLineItemsData,
                lineItemGroup.key
            );
        newLineItemsData = {
            ...newLineItemsData,
            ...finalLineItemsSingleGroupWiseTotalQtyObj,
        };
        if (onChange) {
            onChange(newLineItemsData);
        }
    };

    const onRestoreClick = (revision) => {
        let newLineItemsData = { ...lineItemsData };
        newLineItemsData['form_data'] = revision.form_data;
        newLineItemsData['total'] = getLineItemsTotal(newLineItemsData);
        newLineItemsData['total_qty'] = getLineItemsTotalQty(newLineItemsData);
        let finalLineItemsSingleGroupWiseTotalQtyObj =
            getLineItemsSingleGroupWiseTotalQty(newLineItemsData);
        newLineItemsData = {
            ...newLineItemsData,
            ...finalLineItemsSingleGroupWiseTotalQtyObj,
        };
        onChange(newLineItemsData);
        setReRenderLineItems(true);
    };

    const getLineItemsTotal = (lineItemsData) => {
        let total = 0;
        Object.keys(lineItemsData?.form_data).map((singleGroupId) => {
            const groupData = lineItemsData.form_data[singleGroupId] || [];
            let grpTotal = 0;
            groupData.forEach((singleItem) => {
                grpTotal = grpTotal + numOr0(singleItem.total);
            });
            total = total + grpTotal;
        });
        return total;
    };

    const getLineItemsTotalQty = (lineItemsData) => {
        let totalQty = 0;
        if (!_.isEmpty(lineItemsData)) {
            Object.keys(lineItemsData.form_data).map((singleGroupId) => {
                const groupData = lineItemsData.form_data[singleGroupId] || [];
                let singleGrpTotalQty = 0;
                groupData.forEach((singleItem) => {
                    singleGrpTotalQty += parseFloat(singleItem.qty) || 0;
                });
                totalQty = totalQty + singleGrpTotalQty;
            });
        }
        return totalQty;
    };

    const getLineItemsSingleGroupWiseTotalQty = (
        lineItemsData,
        lineItemGroupKey = undefined
    ) => {
        let lineItemsSingleGroupWiseTotalQtyObj = {};
        if (!_.isEmpty(lineItemsData)) {
            Object.keys(lineItemsData.form_data).map((singleGroupId) => {
                let key = `${singleGroupId}_total_qty`;
                if (
                    (lineItemGroupKey && lineItemGroupKey == singleGroupId) ||
                    !lineItemGroupKey
                ) {
                    const groupData =
                        lineItemsData.form_data[singleGroupId] || [];
                    let singleGrpTotalQty = 0;
                    groupData.forEach((singleItem) => {
                        singleGrpTotalQty =
                            singleGrpTotalQty + numOr0(singleItem.qty);
                    });
                    lineItemsSingleGroupWiseTotalQtyObj[key] =
                        singleGrpTotalQty;
                }
            });
        }
        return { ...lineItemsSingleGroupWiseTotalQtyObj };
    };

    const getCommonQtyLabelFrConfigData = () => {
        return (
            srvcConfigData?.srvc_type_line_item_common_qty_label || 'Quantity'
        );
    };

    if (reRenderLineItems) {
        setTimeout(() => setReRenderLineItems(false), 100);
    }

    let countUpParams = {};
    if (!isSrvcReqLock) {
        countUpParams = {
            decimal: '.',
            decimals: 2,
        };
    }

    //Downloads Excel files containing line item data
    const downloadLineItemsExcelFiles = () => {
        // Check if config object is provided
        if (config) {
            let groupedLineItems = [];
            // Iterate over each group in the config object
            Object.keys(config).forEach((groupId) => {
                const currentGroup = config[groupId];

                // Get column metadata for the current group
                const columnMetaData = getColMeta(currentGroup);
                // Get row data for the current group
                const groupRowData = getRowData(currentGroup, true);

                // Format rows for download
                let formattedDownloadRows = groupRowData.map((row) => {
                    const formattedItem = {};
                    // Iterate over columns in the metadata
                    columnMetaData.forEach((column) => {
                        if (row[column.key] !== undefined) {
                            let rowvalFrmMeta = getValueDataFrmFormMeta(
                                columnMetaData,
                                row
                            );
                            formattedItem[column.label] =
                                rowvalFrmMeta?.[column.key] == 'NaN'
                                    ? ''
                                    : rowvalFrmMeta?.[column.key];
                        }
                    });
                    return formattedItem;
                });

                // Add grouped line items to the list
                groupedLineItems.push({
                    name: currentGroup?.label,
                    items: formattedDownloadRows,
                });
            });

            let downloadFileName = isBrand
                ? 'BrandLineItems_' + title
                : 'SPLineItems_' + title;
            downloadExcelFileWithTabwise(groupedLineItems, downloadFileName);
        }
    };

    return (
        <>
            <h3 className=""> {heading} </h3>
            {(allow_sp_line_items_download ||
                !ConfigHelpers.isServiceProvider() ||
                (ConfigHelpers.isServiceProvider() &&
                    (isCustAccess ||
                        srvcConfigData?.show_line_items_to_sp))) && (
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center">
                    <h3 className=""> </h3>
                    {Object.keys(lineItemDataFrDownload)?.length > 0 && (
                        <div className="gx-mb-2 gx-d-flex gx-justify-content-end">
                            <Button
                                icon={<DownloadOutlined />}
                                className="gx-mb-0 gx-btn-sm"
                                type="primary"
                                onClick={() => downloadLineItemsExcelFiles()}
                            >
                                Download
                            </Button>
                        </div>
                    )}
                </div>
            )}

            {/* Loop through Config */}
            {Object.keys(config).map((singleGroupId) => {
                const singleGroup = config[singleGroupId];
                return (
                    <div key={singleGroupId}>
                        {/* Table Label */}
                        <p>
                            <b>{singleGroup.label}</b>
                        </p>

                        {/* Total Section (Now Between Label & Table) */}
                        <div className="gx-mb-2 gx-d-flex gx-justify-content-between">
                            <div>
                                <span>
                                    Total
                                    <b className="gx-ml-2">
                                        ₹
                                        <CountUp
                                            end={lineItemsData?.total}
                                            duration={0.5}
                                            {...countUpParams}
                                        />
                                    </b>
                                    {
                                        <div className="gx-mb-2 gx-d-flex gx-justify-content-between">
                                            {'Total ' +
                                                getCommonQtyLabelFrConfigData()}
                                            <b className="gx-ml-2">
                                                <CountUp
                                                    end={getLineItemsTotalQty(
                                                        lineItemsData
                                                    )}
                                                    duration={0.5}
                                                    {...countUpParams}
                                                />
                                            </b>
                                        </div>
                                    }
                                </span>
                            </div>
                            <div>
                                {revisions?.length > 0 && (
                                    <Revisions
                                        config={config}
                                        revisionsObj={revisions}
                                        onRestoreClick={onRestoreClick}
                                        readOnly={readOnly || isSrvcReqLock}
                                    />
                                )}
                            </div>
                        </div>

                        {/* Table */}
                        {!reRenderLineItems && (
                            <MetaInputTable
                                edittable
                                colMeta={getColMeta(singleGroup)}
                                rowData={getRowData(singleGroup)}
                                noFilters
                                onChange={(newData) => {
                                    setNewRowData(singleGroup, newData);
                                }}
                                readOnly={readOnly || isSrvcReqLock}
                            />
                        )}
                        <div>
                            <LineItemConfigSummary groupConfig={singleGroup} />
                        </div>
                    </div>
                );
            })}
        </>
    );
};

export default LineItems;
