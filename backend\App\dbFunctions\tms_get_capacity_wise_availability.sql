CREATE OR REPLACE FUNCTION public.tms_get_capacity_wise_availability (
    requester json,
    page_no_ integer,
    page_size_ integer,
    filters_ json,
    search_query text
) RETURNS json LANGUAGE plpgsql AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;

	availability_data json;
	total_count integer;
	offset_val integer;

begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = requester->>'usr_id';
	ip_address_ = requester->>'ip_address';  
	user_agent_ = requester->>'user_agent';
	org_id_ = (requester->>'org_id')::integer;

	-- Calculate offset
	offset_val = (page_no_ - 1) * page_size_;

	-- Get total count
	select count(distinct users.user_id)
	from cl_tx_users as users
	left join cl_tx_usr_availability as availability 
		on users.user_id = availability.user_id 
		and availability.usr_tmzone_day = current_date
		and availability.is_active = true
	where users.org_id = org_id_
		and users.is_active = true
		and (search_query = '' or users.full_name ilike '%' || search_query || '%')
	into total_count;

	-- Get availability data with pagination
	availability_data := array_to_json(array(
		select jsonb_build_object(
			'user_id', users.user_id,
			'full_name', users.full_name,
			'email', users.email,
			'mobile', users.mobile,
			'total_slots', coalesce(slot_counts.total_slots, 0),
			'available_slots', coalesce(slot_counts.available_slots, 0),
			'utilized_slots', coalesce(slot_counts.utilized_slots, 0),
			'availability_status', case 
				when coalesce(slot_counts.available_slots, 0) = 0 then 'Offline'
				when coalesce(slot_counts.available_slots, 0) = coalesce(slot_counts.total_slots, 0) then 'Available'
				else 'Limited'
			end,
			'last_updated', coalesce(slot_counts.last_updated, null)
		)
		from cl_tx_users as users
		left join (
			select 
				availability.user_id,
				count(*) as total_slots,
				count(case when availability.is_available = true then 1 end) as available_slots,
				count(case when availability.is_available = false then 1 end) as utilized_slots,
				max(availability.updated_at) as last_updated
			from cl_tx_usr_availability as availability
			where availability.usr_tmzone_day = current_date
				and availability.is_active = true
			group by availability.user_id
		) as slot_counts on users.user_id = slot_counts.user_id
		where users.org_id = org_id_
			and users.is_active = true
			and (search_query = '' or users.full_name ilike '%' || search_query || '%')
		order by users.full_name
		limit page_size_
		offset offset_val
	));

	status = true;
	message = 'success';
	resp_data := jsonb_build_object(
		'status', status,
		'code', message,
		'data', jsonb_build_object(
			'capacity_data', availability_data,
			'total_count', total_count,
			'page_no', page_no_,
			'page_size', page_size_,
			'total_pages', ceil(total_count::numeric / page_size_::numeric)
		)
	);

	return resp_data;

end;
$function$;
