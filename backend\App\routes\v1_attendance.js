var express = require('express');
var router = express.Router();
var attendance_model = require('../api_models/attendance_model');
var { getUserContextFrmReq } = require('../api_models/utils/authrizor');

router.post('/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.exportAttendanceByEmail(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/availability/proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getViewDataFrAvailabilityForm(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/availability/proto/:entry_id', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    var entry_id = req.params.entry_id;
    attendance_model
        .getAvailabilitySingleEntry(req.query, entry_id)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/availability/overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getAvailabilityOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/availability', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getAllAvailability(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/my_leaves', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getAllMyleaves(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/availability/myleaves_overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getMyLeavesOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/availability', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .createOrUpdateAttendance(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.put('/availability/:entry_id', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    var entry_id = req.params.entry_id;
    attendance_model
        .createOrUpdateAttendance(req.body, entry_id)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/availability/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .exportAvailabilityByEmail(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

//Assignment
router.post('/assignment/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.exportAssignmentByEmail(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/assignment/overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getAssignmentOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/assignment', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getAllAssignment(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

//Site Attendance
router.get('/attendance/overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getAttendanceOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/attendance', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getAllAttendance(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

//Office Attendance
router.get('/office_attendance/overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getOfficeAttendanceOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.get('/office_attendance', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getAllOfficeAttendance(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/office_attendance/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .exportOfficeAttendanceByEmail(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/office_attendance/sendEmail', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getAllAbsentUsersEmailId(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

// Daily Report
router.get('/daily_report', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getDailyreport(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/daily_report/overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getDailyReportOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/daily_report/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .exportDailyReportAttendanceByEmail(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

//Range Report
router.get('/range_report', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model.getRangeReport(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.get('/range_report/overview_proto', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getRangeReportOverviewProto(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/range_report/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .exportRangeReportAttendanceByEmail(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

//Capacity-wise Availability
router.get(
    '/capacity_wise_availability/overview_proto',
    function (req, res, next) {
        const attendance_model = setParamsToModel(req);
        attendance_model
            .getCapacityWiseAvailabilityOverviewProto(req.query)
            .then((operationResp) => {
                res.status(operationResp.httpStatus).send(operationResp.resp);
            });
    }
);

router.get('/capacity_wise_availability', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .getAllCapacityWiseAvailability(req.query)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

router.post('/capacity_wise_availability/export', function (req, res, next) {
    const attendance_model = setParamsToModel(req);
    attendance_model
        .exportCapacityWiseAvailabilityByEmail(req.body)
        .then((operationResp) => {
            res.status(operationResp.httpStatus).send(operationResp.resp);
        });
});

const setParamsToModel = (req) => {
    const attendance_model =
        require('../api_models/attendance_model').getInstance();
    attendance_model.database = req.app.get('db');
    attendance_model.databaseReplica = req.app.get('db_replica');
    attendance_model.databaseDump = req.app.get('db_dump');
    attendance_model.ip_addr = req.ip;
    attendance_model.user_agent = req.get('User-Agent');
    attendance_model.user_context = getUserContextFrmReq(req);
    return attendance_model.getFreshInstance(attendance_model);
};

module.exports = router;
