import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Button,
    Col,
    Modal,
    Popcon<PERSON>rm,
    Popover,
    Progress,
    Result,
    Row,
    Spin,
    Switch,
    Tabs,
    Tag,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import http_utils from '../../util/http_utils';
import {
    NoData,
    convertMomentToDateString,
    convertMomentToLocalDateString,
    getLinktoObject,
} from '../../util/helpers';
import GanttCalendar, {
    formatTime,
} from '../../components/wify-utils/GanttCalendar';
import { FaUserAstronaut } from 'react-icons/fa';
import { DeleteFilled, ReloadOutlined, UserOutlined } from '@ant-design/icons';
import { getAddressText } from './ServiceReqsList';
import { truncateText } from './helper';
import PreviewScreen from './PreviewScreen';
import ConfigHelpers from '../../util/ConfigHelpers';
import CountUp from '../../components/wify-utils/CountUp/countUp';

const protoUrl = '/subtasks/generate-assignment';

const tempTransformSchedule = (lambdaResp) =>
    lambdaResp.calendarData.map((singleUser) => ({
        userDetails: {
            name: singleUser.name,
            id: singleUser.id,
            homeCoords: singleUser.homeCoords,
        },
        jobs: singleUser.schedule.map((singleJob) => ({
            ...singleJob,
            startTime: singleJob.formattedStartTime,
            endTime: singleJob.formattedEndTime,
        })),
    }));

const AutoAssignProgress = (props) => {
    const {
        onClose,
        sbtskData,
        assigneeFilter,
        reqFilter,
        assigneeCount,
        requestPending,
        sbtskDate,
        selectedUsers,
        selectedRequests,
        onSubmitDone,
    } = props || {};
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [progData, setProgData] = useState({
        progress: 10,
        warning: undefined,
    });
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState();
    const [showFadeOut, setShowFadeOut] = useState(false);
    const [hideSuccess, setHideSuccess] = useState(false);
    const [showManualOverrideInfo, setShowManualOverrideInfo] = useState(false);

    // Temp code to check assignment logic
    const [tempTransformedSchedule, setTransformedSchedule] = useState([]);
    const [stats, setStats] = useState([]);
    const [activeTab, setActiveTab] = useState();

    const initViewData = () => {
        if (!isLoadingViewData) {
            setIsLoadingViewData(true);
            // init simulated progress till 5 secs
            initSimulatedProg();
            setViewData(undefined);
            setError(undefined);
            var params = {
                sbtskData,
                assigneeFilter,
                reqFilter,
                selectedUsers,
                selectedRequests,
            };
            const onComplete = (resp) => {
                try {
                    let respData = JSON.parse(resp.data.body);
                    setTransformedSchedule(tempTransformSchedule(respData));
                    setStats(respData.stats); // Set stats
                    initGenerationSuccess(respData);
                } catch (error) {
                    // TODO show a nice error using setProgData
                    // setProgData()
                    console.log('error', error);
                    console.log('resp type', typeof resp, resp);
                    setError(
                        typeof resp == 'string'
                            ? resp
                            : http_utils.decodeErrorToMessage({
                                  response: resp,
                              })
                    );
                    setIsLoadingViewData(false);
                }
            };
            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };
            var url = protoUrl;
            // // console.log('params',params);
            http_utils.performPostCall(url, params, onComplete, onError);
        }
    };
    // CDM
    useEffect(() => {
        initViewData();
    }, [props]);

    const initGenerationSuccess = (respData) => {
        setIsLoadingViewData(false);
        setShowManualOverrideInfo(false);
        // console.log('respData',respData);
        setViewData(respData);
        setTimeout(() => {
            setShowFadeOut(true); // fadeout
            setTimeout(() => {
                setHideSuccess(true);
            }, 1000); // hide success
        }, 1000); // fade in for
    };

    const simulateSuccess = () => {
        setIsLoadingViewData(false); // TODO
        setViewData({}); // TODO
        setTimeout(() => {
            setShowFadeOut(true); // fadeout
            setTimeout(() => {
                setHideSuccess(true);
            }, 1000); // hide success
        }, 1000); // fade in for
    };

    const initSimulatedProg = () => {
        const interval = setInterval(() => {
            setProgData((prev) => {
                if (prev.progress >= 100) {
                    clearInterval(interval); // Stop the interval
                    return { progress: 100 };
                }
                prev.progress = prev.progress + 1;
                return { ...prev };
            });
        }, 75);
    };

    // Code to simulate progress
    // useEffect(() => {
    //     const interval = setInterval(() => {
    //         setProgData((prev) => {
    //             if (prev.progress >= 100) {
    //                 clearInterval(interval);
    //                 simulateSuccess();
    //                 return {progress:100};
    //             }
    //             if(prev.progress == 29){
    //                 prev.warning = {
    //                     message: "Missing start or endtime (5 req.)",
    //                     btnText: "Auto assign time"
    //                 }
    //                 prev.progress = (prev.progress + 1);
    //             }
    //             if(prev.warning==undefined){
    //                 prev.progress = (prev.progress + 1);
    //             }
    //             return {...prev};
    //         });
    //     }, 10);

    //     return () => clearInterval(interval);
    // }, []);

    // console.log('progData',progData?.progress);
    const handleIgnoreWarning = () => {
        setProgData({
            ...progData,
            warning: undefined,
        });
    };

    const switchToFinalPreview = () => {
        setActiveTab('final-preview');
    };

    const renderSingleUser = (text, row, index) => {
        // let title = `🏍️${row.travelTimeMinutes}min to ${row.job.pincode} `
        return (
            <div>
                {text}
                {/* {JSON.stringify(row)} */}
            </div>
        );
    };

    const removeJob = (row) => {
        const modifiedItems = tempTransformedSchedule.map((singleUser) => {
            singleUser.jobs = singleUser.jobs.filter(
                (singleJob) => singleJob.job.id != row.job.id
            );
            return singleUser;
        });
        setTransformedSchedule([...modifiedItems]);
        setShowManualOverrideInfo(true);
    };

    const getLinkFrReq = (reqDetails) => {
        const params = {
            query: reqDetails.title,
            showItemEditor: true,
        };
        const linkToData = getLinktoObject(
            `/services/${reqDetails.srvc_type_id}`,
            params
        );
        const linkToDataFrSrvcPrvdr = getLinktoObject(
            `/customer-requests`,
            params
        );

        if (ConfigHelpers.isSrvcPrvdrBySrvcReqId(reqDetails)) {
            return linkToDataFrSrvcPrvdr;
        } else {
            return linkToData;
        }
    };

    const renderSingleJob = (text, row) => {
        let title = `🏍️${row.travelTimeMinutes}min to ${row.job.pincode} `;
        return (
            <Popover
                trigger="hover"
                title={
                    <div className="gx-d-flex gx-justify-content-between gx-align-items-center wy-gap-10">
                        <div>
                            <Link
                                to={getLinkFrReq(row.job.reqDetails)}
                                target="_blank"
                            >
                                {row.job.reqDetails.title}
                            </Link>
                        </div>
                        {!row.isExistingAlready && (
                            <div>
                                <Popconfirm
                                    title={`Are you sure you want to remove ${row.job.reqDetails.title}?`}
                                    onConfirm={() => removeJob(row)}
                                >
                                    <Button
                                        data-testId="delete-job-btn"
                                        size="small"
                                        className="gx-mb-0 gx-border-0"
                                        icon={<DeleteFilled />}
                                    />
                                </Popconfirm>
                            </div>
                        )}
                    </div>
                }
                content={
                    <div>
                        <p>
                            {row.job.reqDetails.form_data.start_time && (
                                <>
                                    {'Requested time - '}
                                    {row.job.reqDetails.form_data.start_time}
                                </>
                            )}
                        </p>
                        <p>
                            {row.job.reqDetails.form_data.request_description}
                        </p>
                        <p>{getAddressText(row.job.reqDetails)}</p>
                        <p>
                            Customer :{' '}
                            {row.job.reqDetails.form_data.cust_full_name}
                        </p>
                    </div>
                }
            >
                <div
                    style={{
                        backgroundColor: row.isExistingAlready
                            ? '#616161'
                            : '#7981b0',
                        color: 'white',
                        padding: '5px',
                    }}
                    data-testId="single-job-wrapper"
                    className="gx-d-flex gx-justify-content-between "
                >
                    <div>
                        {truncateText(row.job.reqDetails.title, 10)}
                        <br />
                        {title} <br />
                        {/* {row.job.reqDetails.title} <br /> */}
                        {/* {row.job.reqDetails.form_data} <br /> */}
                        {formatTime(row.startTime, 'h:mm')} -{' '}
                        {formatTime(row.endTime, 'h:mm')}
                    </div>
                </div>
            </Popover>
        );
    };

    return (
        <Modal
            visible={true}
            width={2400}
            title={
                <div className="gx-d-flex gx-align-items-center wy-gap-10">
                    <Avatar
                        style={{
                            backgroundColor: '#ff8400',
                        }}
                        className="gx-m-0 gx-pt-1"
                        size="large"
                        icon={<FaUserAstronaut className="gx-fs-2xl" />}
                    />
                    <Tag className="gx-bg-info gx-text-white gx-border-0 gx-m-0">
                        <span className="">
                            <UserOutlined />
                            <CountUp
                                start={0}
                                end={assigneeCount}
                                duration={1}
                            />
                        </span>
                        <span className="gx-m-1">
                            <i className="icon icon-arrow-right gx-mr-1 gx-vertical-align-middle" />
                        </span>
                        <span className="">
                            <i className="icon icon-home gx-mr-1 gx-vertical-align-middle" />
                            <CountUp
                                start={0}
                                end={requestPending}
                                duration={1}
                            />
                        </span>
                    </Tag>
                    <Tag className="gx-bg-primary gx-text-white gx-border-0 gx-m-0">
                        {sbtskData.label}
                    </Tag>
                    <Tag className="gx-bg-green gx-text-white gx-border-0 gx-m-0">
                        {convertMomentToLocalDateString(sbtskDate)}
                    </Tag>
                </div>
            }
            style={{
                marginTop: '-70px',
            }}
            bodyStyle={{
                minHeight: '85vh',
                padding: '18px',
                paddingTop: '0px',
            }}
            footer={null}
            onCancel={onClose}
        >
            {/* {JSON.stringify({ assigneeFilter, reqFilter })} */}
            {error && !viewData && (
                <div className="gx-d-flex gx-justify-content-center gx-align-items-center">
                    <div className="gx-text-center">
                        <p className="gx-text-red">
                            Unable to generate auto assignments - {error}
                        </p>
                        <Button onClick={initViewData}>Retry</Button>
                    </div>
                </div>
            )}
            {isLoadingViewData && (
                <>
                    <div className="gx-mt-5">
                        {progData?.warning ? (
                            <Result
                                className="fadein"
                                status="warning"
                                title={progData.warning.message}
                                extra={
                                    <>
                                        <Button
                                            type="dashed"
                                            danger
                                            size="large"
                                            onClick={onClose}
                                        >
                                            Cancel
                                        </Button>
                                        <Button
                                            type="primary"
                                            size="large"
                                            onClick={handleIgnoreWarning}
                                        >
                                            {progData.warning.btnText}
                                        </Button>
                                    </>
                                }
                            />
                        ) : (
                            <div className="gx-text-center fadein">
                                <Progress
                                    type="circle"
                                    width={80}
                                    strokeColor={{
                                        from: '#108ee9',
                                        to: '#87d068',
                                    }}
                                    percent={progData?.progress}
                                    status="active"
                                />
                                <p>
                                    <Spin />
                                    Auto assign in progress
                                </p>
                            </div>
                        )}
                    </div>
                </>
            )}
            {viewData && (
                <>
                    {!hideSuccess && (
                        <Result
                            className={`gx-mt-2 ${showFadeOut ? 'fadeout' : 'fadein'}`}
                            status="success"
                            title="Successfully generated assignments!"
                        />
                    )}
                    {hideSuccess && (
                        <div className="gx-mt-2">
                            <div className="gx-d-flex gx-justify-content-between gx-align-items-center">
                                {/* <div className='h2 gx-border gx-card-widget gx-px-3 gx-py-2'> */}
                                <div>
                                    <Alert
                                        message={
                                            <>
                                                {stats.map((stat, index) => (
                                                    <span
                                                        key={index}
                                                        className={stat.color}
                                                    >
                                                        {stat.text}
                                                        {index <
                                                            stats.length -
                                                                1 && (
                                                            <span className="gx-mx-2">
                                                                |
                                                            </span>
                                                        )}
                                                    </span>
                                                ))}
                                            </>
                                        }
                                        type="info"
                                        className="gx-m-0"
                                        showIcon
                                        icon={
                                            <>
                                                <Avatar
                                                    style={{
                                                        backgroundColor:
                                                            '#ff8400',
                                                    }}
                                                    size="small"
                                                    className="gx-m-0 gx-text-white gx-mr-2"
                                                    icon={<FaUserAstronaut />}
                                                />
                                                {/* <span className="gx-mr-2">{">"}</span> */}
                                            </>
                                        }
                                    />
                                </div>
                                <div>
                                    <Button
                                        icon={<ReloadOutlined />}
                                        size="large"
                                        onClick={initViewData}
                                        className="gx-my-0"
                                    >
                                        Regenerate
                                    </Button>
                                    <Button
                                        type="primary"
                                        className="gx-my-0"
                                        size="large"
                                        onClick={switchToFinalPreview}
                                    >
                                        FINAL PREVIEW
                                    </Button>
                                </div>
                            </div>
                            <div className="gx-mt-3" />
                            {showManualOverrideInfo && (
                                <Alert
                                    message="Draft is manually modified! The above numbers will not match with the current draft."
                                    type="error"
                                    showIcon
                                />
                            )}
                            <Tabs
                                activeKey={activeTab}
                                onTabClick={setActiveTab}
                                tabBarExtraContent={
                                    <div className="gx-d-flex gx-align-items-center wy-gap-10">
                                        <Switch
                                            className="gx-d-none"
                                            // onChange={handleToggle}
                                            checkedChildren="Show unassigned"
                                            unCheckedChildren="Show unassigned"
                                            // title='Show unassigned'
                                        />
                                        <Tag
                                            color="green"
                                            className="gx-fs-xl gx-m-0 gx-px-2 gx-py-1"
                                        >
                                            {convertMomentToLocalDateString(
                                                sbtskDate
                                            )}
                                        </Tag>
                                    </div>
                                }
                            >
                                <Tabs.TabPane key="calendar" tab="Calendar">
                                    <GanttCalendar
                                        // demoMode
                                        GANTT_START_TIME={
                                            viewData.startHour || 9
                                        }
                                        GANTT_END_TIME={viewData.endHour || 18}
                                        GANTT_TIME_INCREMENT={60}
                                        items={tempTransformedSchedule}
                                        customItemRender={renderSingleJob}
                                        customUserRender={renderSingleUser}
                                    />
                                </Tabs.TabPane>
                                <Tabs.TabPane
                                    key="final-preview"
                                    tab="Final Preview"
                                >
                                    <div data-testid="final-preview-tab">
                                        <PreviewScreen
                                            sbtskDate={convertMomentToDateString(
                                                sbtskDate
                                            )}
                                            sbtskData={sbtskData}
                                            schedule={tempTransformedSchedule}
                                            onSubmitDone={onSubmitDone}
                                        />
                                    </div>
                                </Tabs.TabPane>
                            </Tabs>
                        </div>
                    )}
                </>
            )}
        </Modal>
    );
};

export default AutoAssignProgress;
