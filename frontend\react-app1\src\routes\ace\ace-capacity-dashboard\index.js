import React, { useState, useEffect } from 'react';
import { Col, Row, Button, Form } from 'antd';
import Auxiliary from 'util/Auxiliary';
import Widget from '../../../components/Widget';
import CircularProgress from '../../../components/CircularProgress';
import StateWiseCapacity from '../../../components/WIFY/dashboard/CapacityDashboard/StateWiseCapacity';
import PincodesNotInHubs from '../../../components/WIFY/dashboard/CapacityDashboard/PincodesNotInHubs';
import http_utils from '../../../util/http_utils';
import FormBuilder from 'antd-form-builder';
import { getLinktoObject } from '../../../util/helpers';
import OrdersWithoutHub from '../../../components/WIFY/dashboard/CapacityDashboard/OrdersWithoutHub';

const protoUrl = '/ace-capacity-dashboard/overview-proto';

const CapacityDashboard = (props) => {
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState('');
    const [isFormSubmitting, setIsFormSubmitting] = useState(false);

    // Get vertical_id from URL params
    let params = {};
    let matchParams = { ...props.match.params };
    Object.keys(matchParams).map((singleParamKey) => {
        if (matchParams[singleParamKey]) {
            params[singleParamKey] = parseInt(matchParams[singleParamKey]);
        }
    });
    const selectedVertical = params['vertical_id'];

    // Load verticals list on component mount
    useEffect(() => {
        initViewData();
    }, [selectedVertical]);

    // Load dashboard data for selected vertical
    const initViewData = () => {
        if (isLoadingViewData) return;

        setIsLoadingViewData(true);
        setViewData(undefined);
        setError(undefined);
        let params = {
            vertical_id: selectedVertical,
        };
        const onComplete = (resp) => {
            setIsLoadingViewData(false);
            setViewData(resp.data);
        };
        const onError = (error) => {
            setIsLoadingViewData(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performGetCall(protoUrl, params, onComplete, onError);
    };

    // Form metadata for vertical selection
    const getVerticalSelectMeta = () => {
        return {
            formItemLayout: null,
            fields: [
                {
                    key: 'vertical_id',
                    label: 'Vertical',
                    widget: 'select',
                    placeholder: 'Choose Vertical',
                    options: viewData?.vertical_list?.filter(
                        (item) => item.label !== null
                    ),
                    required: true,
                    tooltip:
                        'All verticals that you as a user have access to will be shown here.',
                    widgetProps: {
                        allowClear: true,
                        showSearch: true,
                        optionFilterProp: 'children',
                        style: {
                            width: '200px',
                        },
                    },
                },
            ],
        };
    };

    // Handle vertical selection
    const onVerticalSelected = (data) => {
        const { vertical_id } = data;
        // Update URL with selected vertical
        let newUrl = '/main/ace/capacity-dashboard/' + vertical_id;
        const history = props.history;
        history.push(getLinktoObject(newUrl, {}));
    };

    if (isLoadingViewData) {
        return (
            <div className="gx-loader-view gx-loader-position">
                <CircularProgress />
            </div>
        );
    } else if (viewData == null) {
        return (
            <div>
                <p className="gx-text-red">{error}</p>
                <Button
                    type="secondary"
                    className=" gx-my-2"
                    onClick={() => initViewData()}
                >
                    Retry
                </Button>
            </div>
        );
    }

    if (viewData?.vertical_list?.length == 1 && !selectedVertical) {
        // simulate auto selection of vertical
        onVerticalSelected({
            vertical_id: viewData?.vertical_list?.[0]?.value,
        });
    }

    return (
        <div>
            {/* Vertical Selection Form */}
            <div className="gx-app-module gx-mb-3 gx-p-4">
                <Form
                    layout="inline"
                    onFinish={(data) => {
                        onVerticalSelected(data);
                    }}
                    initialValues={params}
                >
                    <FormBuilder meta={getVerticalSelectMeta()} />

                    <Form.Item>
                        <Button type="primary" htmlType="submit">
                            Open
                        </Button>
                    </Form.Item>
                    {isFormSubmitting ? (
                        <div className="gx-loader-view gx-loader-position">
                            <CircularProgress />
                        </div>
                    ) : null}
                </Form>
            </div>

            {/* Dashboard Content - Only shown when vertical is selected */}
            {selectedVertical && (
                <>
                    <Row className="gx-mb-4">
                        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                            <StateWiseCapacity
                                verticalId={selectedVertical}
                                parentViewData={viewData}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                            <PincodesNotInHubs
                                verticalId={selectedVertical}
                                parentViewData={viewData}
                            />
                        </Col>
                    </Row>
                    <Row>
                        <Col xs={24} sm={24} md={24} lg={24} xl={24}>
                            <OrdersWithoutHub
                                verticalId={selectedVertical}
                                parentViewData={viewData}
                            />
                        </Col>
                    </Row>
                </>
            )}
        </div>
    );
};

export default CapacityDashboard;
