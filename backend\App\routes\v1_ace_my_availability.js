var express = require('express');
const { getUserContextFrmReq } = require('../api_models/utils/authrizor');
var router = express.Router();

router.get('/day', function (req, res, next) {
    const model = require('../api_models/ace/my_availability_model');
    setParamsToModel(req, model);
    model.getSlotsForTheDay(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

router.post('/', function (req, res, next) {
    const model = require('../api_models/ace/my_availability_model');
    setParamsToModel(req, model);
    model.createOrUpdateAvailability(req.body).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

//create route for date range api
router.get('/range', function (req, res, next) {
    const model = require('../api_models/ace/my_availability_model');
    setParamsToModel(req, model);
    model.getSlotsForDateRange(req.query).then((operationResp) => {
        res.status(operationResp.httpStatus).send(operationResp.resp);
    });
});

const setParamsToModel = (req, model) => {
    model.database = req.app.get('db');
    model.ip_addr = req.ip;
    model.user_agent = req.get('User-Agent');
    model.user_context = getUserContextFrmReq(req);
};

module.exports = router;
