import { DatePicker } from 'antd';
import moment, { utc } from 'moment';
import {
    getColorCodeFrStatusCategory,
    getPresetRangesForRangeDatePicker,
} from '../../../util/helpers';

const filters = [
    {
        key: 'days',
        label: 'Days',
        widget: DatePicker.RangePicker,
        widgetProps: {
            ranges: getPresetRangesForRangeDatePicker(),
            defaultValue: [moment().utc().startOf('day'), moment().utc()],
        },
    },
    {
        key: 'approval',
        label: 'Approval',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [
            {
                label: 'Any',
                value: '-1',
                color: 'gx-bg-grey',
            },
            {
                label: 'Pending',
                value: 'Pending',
                color: '#e57373',
            },
            {
                label: 'Approved',
                value: 'Approved',
                color: getColorCodeFrStatusCategory('CLOSED'),
            },
            {
                label: 'Rejected',
                value: 'Rejected',
                color: 'gx-bg-grey',
            },
        ],
    },
    {
        key: 'fully_present',
        label: 'Fully Present',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [
            {
                label: 'Any',
                value: '-1',
                color: 'gx-bg-grey',
            },
            {
                label: 'Yes',
                value: true,
                color: getColorCodeFrStatusCategory('CLOSED'),
            },
            {
                label: 'No',
                value: false,
                color: getColorCodeFrStatusCategory('ACTIVE'),
            },
        ],
    },
    {
        key: 'locations',
        label: 'Location group',
        widget: 'select',
        placeholder: 'Select..',
        // "quick" : true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    },
    {
        key: 'technician_roles',
        label: 'Roles',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    },
];

export { filters };
