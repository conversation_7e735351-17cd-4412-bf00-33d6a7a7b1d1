import { DatePicker } from 'antd';
import moment, { utc } from 'moment';
import {
    getColorCodeFrStatusCategory,
    getPresetRangesForRangeDatePicker,
} from '../../../util/helpers';

const filters = [
    {
        key: 'days',
        label: 'Days',
        widget: DatePicker.RangePicker,
        widgetProps: {
            ranges: getPresetRangesForRangeDatePicker(),
            defaultValue: [moment().utc().startOf('day'), moment().utc()],
        },
    },
    {
        key: 'locations',
        label: 'Location group',
        widget: 'select',
        placeholder: 'Select..',
        // "quick" : true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    },
    {
        key: 'technician_roles',
        label: 'Roles',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    },
    //add filter servive hub and availablity
    {
        key: 'service_hubs',
        label: 'Service Hubs',
        widget: 'select',
        placeholder: 'Select..',
        // "quick" : true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [],
    },
    {
        key: 'availability',
        label: 'Availability',
        placeholder: 'Select..',
        widget: 'select',
        quick: true,
        widgetProps: {
            // make sure to add mode as multiple when its for quick
            mode: 'multiple',
            optionFilterProp: 'children',
        },
        options: [
            {
                label: 'Fully Present',
                value: 'fully_present',
                color: getColorCodeFrStatusCategory('CLOSED'),
            },
            {
                label: 'Limited',
                value: 'limited',
                color: getColorCodeFrStatusCategory('ACTIVE'),
            },
            {
                label: 'Offline',
                value: 'offline',
                color: getColorCodeFrStatusCategory('ACTIVE'),
            },
        ],
    },
];

export { filters };
