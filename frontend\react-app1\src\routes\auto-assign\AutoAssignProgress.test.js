import React from 'react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { render, waitFor, screen, act } from '@testing-library/react';
import AutoAssignProgress from './AutoAssignProgress';
import http_utils from '../../util/http_utils';
import userEvent from '@testing-library/user-event';
import PreviewScreen from './PreviewScreen';
import moment from 'moment';
import GanttCalendar from '../../components/wify-utils/GanttCalendar';
import ConfigHelpers from '../../util/ConfigHelpers';

jest.mock(`../../util/http_utils`);
jest.mock('./PreviewScreen');
jest.mock('../../components/wify-utils/GanttCalendar');
jest.setTimeout(5000); // Increase timeout to 15 seconds for this file
const animationDelay = 1500; //ms
const prefix = 'AutoAssgnPrg';

beforeEach(() => {
    jest.clearAllMocks();
    PreviewScreen.mockImplementation((props) => {
        console.info('previewscreen rendered');
        return <div></div>;
    });
    GanttCalendar.mockImplementation((props) => {
        return <div></div>;
    });
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
});
const mockOnSubmitDone = jest.fn();
const mockPostCall = (callback) => {
    http_utils.performPostCall.mockImplementation(
        (path, params, onComplete, onError, avoidPathPrefix = false) => {
            callback({ path, params, onComplete, onError, avoidPathPrefix });
        }
    );
};

test(`${prefix} smoke test`, async () => {
    render(
        <AutoAssignProgress
            sbtskData={{
                label: 'Test type 1',
            }}
        />
    );
});

test(`${prefix} passes selectedUsers & requests to API`, async () => {
    mockPostCall(({ params, onComplete }) => {
        expect(params).toEqual(
            expect.objectContaining({
                selectedUsers: [1, 2, 3],
                selectedRequests: [4, 5, 6],
            })
        );

        onComplete({
            data: {
                body: JSON.stringify({
                    stats: [],
                    calendarData: [],
                }),
            },
        });
    });

    render(
        <AutoAssignProgress
            sbtskData={{
                label: 'Test type 1',
                value: '1',
            }}
            selectedUsers={[1, 2, 3]}
            selectedRequests={[4, 5, 6]}
        />
    );

    await waitFor(() =>
        expect(http_utils.performPostCall).toHaveBeenCalledTimes(1)
    );
});

test(`${prefix} regenerate passes selectedUsers & requests to API`, async () => {
    mockPostCall(({ params, onComplete }) => {
        expect(params).toEqual(
            expect.objectContaining({
                selectedUsers: [1, 2, 3],
                selectedRequests: [4, 5, 6],
            })
        );
        onComplete({
            data: {
                body: JSON.stringify({
                    stats: [],
                    calendarData: [],
                }),
            },
        });
    });

    render(
        <AutoAssignProgress
            sbtskData={{
                label: 'Test type 1',
                value: '1',
            }}
            selectedUsers={[1, 2, 3]}
            selectedRequests={[4, 5, 6]}
        />
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const regenerateButton = await screen.findByText('Regenerate');

    userEvent.click(regenerateButton);
    await waitFor(() =>
        expect(http_utils.performPostCall).toHaveBeenCalledTimes(2)
    );
});

// Final preview click opens the preview final preview tab
test(`${prefix} final preview click opens the preview final preview tab`, async () => {
    mockPostCall(({ params, onComplete }) => {
        onComplete({
            data: {
                body: JSON.stringify({
                    stats: [],
                    calendarData: [],
                }),
            },
        });
    });

    render(
        <AutoAssignProgress
            sbtskData={{
                label: 'Test type 1',
                value: '1',
            }}
        />
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const finalPreviewButton = await screen.findByText('FINAL PREVIEW');
    expect(finalPreviewButton).toBeInTheDocument();
    act(() => {
        userEvent.click(finalPreviewButton);
    });

    const finalPreviewTab = await screen.findByTestId('final-preview-tab');
    expect(finalPreviewTab).toBeInTheDocument();
    expect(finalPreviewTab).toBeVisible();
});
// test if final preview is rendered and passed required props
test(`${prefix} final preview is rendered and passed required props`, async () => {
    PreviewScreen.mockImplementation((props) => {
        expect(props).toEqual(
            expect.objectContaining({
                schedule: expect.any(Array),
                sbtskData: expect.any(Object),
                sbtskDate: expect.any(String),
            })
        );
        return <div></div>;
    });
    mockPostCall(({ params, onComplete }) => {
        onComplete({
            data: {
                body: JSON.stringify({
                    stats: [],
                    calendarData: [],
                }),
            },
        });
    });

    render(
        <AutoAssignProgress
            sbtskData={{
                label: 'Test type 1',
                value: '1',
            }}
            sbtskDate={moment()}
            onSubmitDone={mockOnSubmitDone}
        />
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const finalPreviewButton = await screen.findByText('FINAL PREVIEW');
    expect(finalPreviewButton).toBeInTheDocument();
    act(() => {
        userEvent.click(finalPreviewButton);
    });
    expect(PreviewScreen).toHaveBeenCalledWith(
        expect.objectContaining({
            onSubmitDone: mockOnSubmitDone,
        }),
        {}
    );
});
const mockGenAssignmentResp = {
    calendarData: [
        {
            id: '4227368d-01bc-4800-ba3c-c6354f890987',
            name: 'BOMM13241086-Shankar Mali',
            homeCoords: { lat: 19.1239285, lng: 72.90944069999999 },
            interestPincodes:
                '400054,400061,400093,400076, 400066, 400068, 400101, 400102, 400103,400059,400053,400069,',
            schedule: [
                {
                    job: {
                        id: 2219122,
                        coords: { lat: 19.1206078, lng: 72.89791629999999 },
                        pincode: '400076',
                        reqDetails: {
                            id: 2219122,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-20T08:49:38.381126',
                            title: 'IKEA-ANKIT-240820457986',
                            c_time: '2024-08-20T08:49:38.381126',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                org_id: 22,
                                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                                comment:
                                    'Dear ANKIT SHRIVASTAVA, Your request no:IKEA-ANKIT-240820457986 has been registered with WIFY and will be attended shortly.',
                                end_time: '12:00PM',
                                entry_id: 2219122,
                                cust_city: 'Mumbai',
                                new_prvdr: '2',
                                cust_state: 'Maharashtra',
                                ip_address: null,
                                start_time: '10:00AM',
                                user_agent: null,
                                cust_line_0: '1101',
                                cust_line_1: 'Bank of Baroda Officer Flats',
                                cust_line_2: 'Chandivali',
                                cust_mobile: '**********',
                                is_api_call: 1,
                                cust_pincode: '400076',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'ANKIT SHRIVASTAVA',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21',
                                notification_data: true,
                                is_customer_access: 0,
                                request_description:
                                    'Sliding Door Solitary Wardrobe with Drawers(1)',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-20T08:49:38.381126',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.1206078,
                                        lng: 72.89791629999999,
                                    },
                                    location_type: 'ROOFTOP',
                                },
                                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                                '0ed2668e-09d2-4feb-b091-e452713ab09f':
                                    '885.00',
                                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                                '********-d80c-4169-89d5-3930f07de2d8': '',
                                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                                    'Sliding Door Solitary Wardrobe with Drawers :\t 1 * 750 - Non Customizable Wardrobe  ',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'COD Payment',
                                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                                    '8de7ad42-8380-419e-a08d-4a65eb0ba179',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'Sliding Door Solitary Wardrobe with Drawers',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '63b0db07-419e-49bf-a416-bfab39b22445',
                                'af2cec16-ec25-4df6-a472-e467ce7a048b': 620523,
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10am - 12pm',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '885',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '10:30AM',
                    formattedEndTime: '11:15AM',
                    travelTimeMinutes: 4,
                    travelDistanceKm: 1.489,
                },
            ],
            currentTime: 600,
            currentCoords: { lat: 19.1239285, lng: 72.90944069999999 },
            totalTravelTimeMinutes: 4,
            totalTravelDistanceKm: 1.489,
        },
        {
            id: '0cfc7ecc-6a68-4db6-9fe4-6f5f9939e3c4',
            name: 'BOMM13241094-Nurul Hasan Shaikh',
            homeCoords: { lat: 19.0521122, lng: 72.900668 },
            interestPincodes:
                '400071,400074,400089,400088,400086,400037,400031,400024,400098,400077,400078',
            schedule: [
                {
                    job: {
                        id: 2135252,
                        coords: { lat: 19.0969562, lng: 72.9197207 },
                        pincode: '400086',
                        reqDetails: {
                            id: 2135252,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-07T14:47:58.042978',
                            title: 'IKEA-NILESH-240807121047',
                            c_time: '2024-08-07T14:47:58.042978',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                host_d: 'tms.wify.co.in',
                                org_id: 2,
                                usr_id: '19fe6b29-aba4-45f9-b4f2-272c828b00ab',
                                comment:
                                    'Dear Nilesh, Your request no:IKEA-NILESH-240807121047 has been registered with WIFY and will be attended shortly.',
                                end_time: '2:00PM',
                                entry_id: 2135252,
                                cust_city: 'Mumbai',
                                mic_files: {},
                                new_prvdr: '2',
                                cust_state: 'Maharashtra',
                                ip_address: '*************:22095',
                                new_status: 'open',
                                start_time: '12:00PM',
                                user_agent:
                                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                                attachments: {},
                                cust_line_0: '1607 P2',
                                cust_line_1: 'Promenade 2',
                                cust_line_2: 'Godrej & Boyce Industry Estate',
                                cust_mobile: '9870991045',
                                is_api_call: 1,
                                sbtsk_db_id: 1954044,
                                camera_files: {},
                                cust_pincode: '400086',
                                srvc_type_id: '41',
                                feedback_data: {
                                    form_data: {
                                        o: '2',
                                        t: '41',
                                        token: 'JmZlu7yNtghRZnEc',
                                        org_id: '2',
                                        entry_id: 'IKEA-NILESH-240807121047',
                                        ip_address: '*************:53014',
                                        user_agent:
                                            'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36',
                                        attachments: {},
                                        display_code:
                                            'IKEA-NILESH-240807121047',
                                        srvc_type_id: '41',
                                        feedback_token: 'JmZlu7yNtghRZnEc',
                                        '793113e6-a49a-4264-b96e-87974b2fb153':
                                            'Super service and great work by Carpenter ',
                                        'd4598efa-2924-4ae8-affa-0d9c21a9fa83': 5,
                                    },
                                    form_meta: [
                                        {
                                            key: 'd4598efa-2924-4ae8-affa-0d9c21a9fa83',
                                            label: 'How would you rate the service experience?',
                                            required: true,
                                            cust_widget: 'Rating',
                                            widgetProps: { count: 5 },
                                        },
                                        {
                                            key: '793113e6-a49a-4264-b96e-87974b2fb153',
                                            label: 'Feedback comments',
                                            widget: 'textarea',
                                            required: false,
                                        },
                                    ],
                                    timestamp: '2024-08-10 14:55:16.350807',
                                    rating_field_key:
                                        'd4598efa-2924-4ae8-affa-0d9c21a9fa83',
                                },
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'Nilesh',
                                feedback_token: 'JmZlu7yNtghRZnEc',
                                is_frm_frontend: true,
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21T00:00:00.000Z',
                                notification_data: true,
                                is_customer_access: 0,
                                update_for_comment: false,
                                request_description:
                                    'Revisit / Fitting done / Wardrobe with 2 doors + 3 drawers(1)',
                                srvc_type_his_db_id: '6553',
                                feedback_given_times: 1,
                                prvdr_assg_timestamp:
                                    '2024-08-07T14:47:58.042978',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.0969562,
                                        lng: 72.9197207,
                                    },
                                    location_type: 'ROOFTOP',
                                },
                                lastChangedFileSectionIds: [],
                                configure_consumer_otp_verification: 3427,
                                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                                '0ed2668e-09d2-4feb-b091-e452713ab09f':
                                    '1062.00',
                                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                                '2219be24-bd31-40c2-a793-a1c0535f6c4c': 500,
                                '4145d8b2-223f-4952-9397-e756f8595299': 1,
                                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                                '********-d80c-4169-89d5-3930f07de2d8': '',
                                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                                    'Wardrobe with 2 doors + 3 drawers :\t 1 * 900 - Non Customizable Wardrobe  ',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'COD Payment',
                                '760f9076-533d-40aa-aac2-eec54aeb5fc0':
                                    '998db8bc-45de-40d3-8ba7-b15b528821a5',
                                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                                    '8ebd24fc-60b8-4b3a-8e34-f1ce66cf763e',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'Wardrobe with 2 doors + 3 drawers',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '9193515a-14b9-4261-860c-b118b31ee901',
                                'af2cec16-ec25-4df6-a472-e467ce7a048b': 324157,
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '12pm - 2pm',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1062',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '10:30AM',
                    formattedEndTime: '11:15AM',
                    travelTimeMinutes: 26,
                    travelDistanceKm: 8.334,
                },
            ],
            currentTime: 600,
            currentCoords: { lat: 19.0521122, lng: 72.900668 },
            totalTravelTimeMinutes: 26,
            totalTravelDistanceKm: 8.334,
        },
        {
            id: '4b2eb733-2a1d-4f34-bd84-36927954be4f',
            name: 'BOMM14241123-Guddu Bahadur',
            homeCoords: { lat: 19.0269012, lng: 72.8553773 },
            interestPincodes: '400019, 400012, 400013, 400005, 400008',
            schedule: [],
            currentTime: 600,
            currentCoords: { lat: 19.0269012, lng: 72.8553773 },
            totalTravelTimeMinutes: 0,
            totalTravelDistanceKm: 0,
        },
        {
            id: 'f61818f1-6225-4a86-bbd4-7166656e6cfb',
            name: 'BOMM17241238-Raju Dhangar',
            homeCoords: { lat: 19.0389455, lng: 72.8420546 },
            interestPincodes:
                '400016,400017,400021,400018,400022,400098,400055,400051,400037,4000031',
            schedule: [
                {
                    job: {
                        id: 2219426,
                        coords: { lat: 19.006572, lng: 72.8298203 },
                        pincode: '400016',
                        reqDetails: {
                            id: 2219426,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-20T09:32:27.524882',
                            title: 'IKEA-S.RAJGOPAL-240820528022',
                            c_time: '2024-08-20T09:32:27.524882',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                host_d: 'tms.wify.co.in',
                                org_id: 22,
                                usr_id: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                comment:
                                    'Dear S.Rajgopalan, Your request no:IKEA-S.RAJGOPAL-240820528022 has been registered with WIFY and will be attended shortly.',
                                entry_id: 2219426,
                                cust_city: 'Mumbai',
                                mic_files: {},
                                new_prvdr: '2',
                                cust_email: null,
                                cust_state: 'Maharashtra',
                                ip_address: '*************:59818',
                                user_agent:
                                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                                attachments: {},
                                cust_line_0: '103',
                                cust_line_1: 'Nestle 1 Apartments',
                                cust_line_2: 'Pandurang Budhkar Strret',
                                cust_line_3: null,
                                cust_mobile: '9790831973',
                                camera_files: {},
                                cust_pincode: '400016',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'S.Rajgopalan',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21T00:00:00.000Z',
                                notification_data: true,
                                is_customer_access: 0,
                                request_description: 'PLATSA drawer Unit (1)',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-20T09:32:27.524882',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.006572,
                                        lng: 72.8298203,
                                    },
                                    location_type: 'GEOMETRIC_CENTER',
                                },
                                lastChangedFileSectionIds: [],
                                '0ed2668e-09d2-4feb-b091-e452713ab09f': '472',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'PLATSA drawer Unit',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '63b0db07-419e-49bf-a416-bfab39b22445',
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10 am - 12 pm ',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '472',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '10:30AM',
                    formattedEndTime: '11:15AM',
                    travelTimeMinutes: 19,
                    travelDistanceKm: 6.435,
                },
            ],
            currentTime: 600,
            currentCoords: { lat: 19.0389455, lng: 72.8420546 },
            totalTravelTimeMinutes: 19,
            totalTravelDistanceKm: 6.435,
        },
        {
            id: 'c7d66ad5-2f9b-4089-8b3f-3828889f4098',
            name: 'BOMM38200277-Chandrakant Kunde',
            homeCoords: { lat: 19.0521122, lng: 72.900668 },
            interestPincodes:
                '400071,400093,400074,400037,400077,400088,400089,400705,400706,4000703',
            schedule: [],
            currentTime: 600,
            currentCoords: { lat: 19.0521122, lng: 72.900668 },
            totalTravelTimeMinutes: 0,
            totalTravelDistanceKm: 0,
        },
        {
            id: '9ce7b236-a0de-4af7-8e46-8766896d2406',
            name: 'BOMMI6241178-Ishwari Prasad',
            homeCoords: { lat: 19.1799265, lng: 73.0287209 },
            interestPincodes: '400001, 400002, 400004, 400012, 400013, 400028',
            schedule: [
                {
                    job: {
                        id: 2190751,
                        coords: { lat: 18.9454805, lng: 72.8352235 },
                        pincode: '400001',
                        reqDetails: {
                            id: 2190751,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-16T04:27:52.280464',
                            title: 'IKEA-SHABBIR-240816289033',
                            c_time: '2024-08-16T04:27:52.280464',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                host_d: 'tms.wify.co.in',
                                org_id: 22,
                                usr_id: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                comment:
                                    'Dear Shabbir Mandviwala, Your request no:IKEA-SHABBIR-240816289033 has been registered with WIFY and will be attended shortly.',
                                entry_id: 2190751,
                                cust_city: 'Mumbai',
                                mic_files: {},
                                new_prvdr: '2',
                                cust_state: 'MAHARASHTRA',
                                ip_address: '**************:62499',
                                user_agent:
                                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                                attachments: {
                                    general: [
                                        'org_22/5b18ab29-e26a-4d22-b588-1877ed8e95bc_228418958830292_taEzBamrm3wm1cjG.jpg',
                                        'org_22/8b6d68da-f29e-4723-8370-75a0dd9ce779_229675103568947_RzQyZldZW42rpUTp.jpg',
                                    ],
                                },
                                cust_line_0: 'E 1303',
                                cust_line_1: 'Green Stone Heritage',
                                cust_line_2: 'opp to Haj House M R A Road ',
                                cust_line_3:
                                    'Next to Crawford Market, Mumbai - 400001',
                                cust_mobile: '9821033372',
                                camera_files: {},
                                cust_pincode: '400001',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'Shabbir Mandviwala',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21T00:00:00.000Z',
                                notification_data: true,
                                is_customer_access: 0,
                                update_for_comment: false,
                                request_description: 'List attached Below',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-16T04:27:52.280464',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 18.9454805,
                                        lng: 72.8352235,
                                    },
                                    location_type: 'ROOFTOP',
                                },
                                lastChangedFileSectionIds: [
                                    'general',
                                    'general',
                                ],
                                '0ed2668e-09d2-4feb-b091-e452713ab09f': '9499',
                                '********-d80c-4169-89d5-3930f07de2d8':
                                    'IMPS/************/Shabbir Ka/BANK NO/XX3372/NA',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'online',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'List attached Below',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '9193515a-14b9-4261-860c-b118b31ee901',
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 17,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10 am - 12 pm ',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '9499',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '11:30AM',
                    formattedEndTime: '12:15PM',
                    travelTimeMinutes: 75,
                    travelDistanceKm: 44.566,
                },
            ],
            currentTime: 600,
            currentCoords: { lat: 19.1799265, lng: 73.0287209 },
            totalTravelTimeMinutes: 75,
            totalTravelDistanceKm: 44.566,
        },
        {
            id: '3f3bda47-a247-4f21-a221-e6d62d25aae8',
            name: 'TNAM15200462-Sunil Gupta',
            homeCoords: { lat: 19.2184129, lng: 72.994773 },
            interestPincodes:
                '400601,400605,400612,400601,400602,400603,400604,400606,400607,400608,400609,400610,400615,400617,400105,401016,401100,401101,401104,401105,401106,401107,401200,401400,401406,400601,400601,400603,400605,400612,400706,400709,410101,412208,421001,421102,421201,421201,421202,421203,421204,421205,421300,421301,421304,421306,421001,421002,421003,421004,421005,421501,421502,421503,421505,421506,421102,421103,421401,421402,421403,421603,421605,421101,421302,421303,421305,421308,421311,421312,421601,421604,421203,421501,421502,421506\n\n,421503,421605,425409',
            schedule: [
                {
                    job: {
                        id: 2207427,
                        coords: { lat: 19.2182456, lng: 72.9620041 },
                        pincode: '400606',
                        reqDetails: {
                            id: 2207427,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-18T08:17:54.308243',
                            title: 'IKEA-MAYURESH-240818382232',
                            c_time: '2024-08-18T08:17:54.308243',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                org_id: 22,
                                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                                comment:
                                    'Dear Mayuresh Gore, Your request no:IKEA-MAYURESH-240818382232 has been registered with WIFY and will be attended shortly.',
                                end_time: '12:00PM',
                                entry_id: 2207427,
                                cust_city: 'Thane',
                                new_prvdr: '2',
                                cust_state: 'Maharashtra',
                                ip_address: null,
                                start_time: '10:00AM',
                                user_agent: null,
                                cust_line_0: '18C',
                                cust_line_1: 'Cosmos Horizon Phase II',
                                cust_line_2: 'Pokhran Road 2',
                                cust_mobile: '9833042624',
                                is_api_call: 1,
                                cust_pincode: '400606',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'Mayuresh Gore',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21',
                                notification_data: true,
                                is_customer_access: 0,
                                request_description: 'Bunk Bed(1)',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-18T08:17:54.308243',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.2182456,
                                        lng: 72.9620041,
                                    },
                                    location_type: 'GEOMETRIC_CENTER',
                                },
                                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                                '0ed2668e-09d2-4feb-b091-e452713ab09f':
                                    '649.00',
                                '1906229a-b49d-4b28-927e-7c2d44971fb4':
                                    '833.00',
                                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                                '********-d80c-4169-89d5-3930f07de2d8':
                                    'pay_OmIGGj7jdO81aJ',
                                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                                    'Bunk Bed :\t 1 * 550 - Beds  ',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'Razor pay',
                                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                                    'bc858215-0475-48e2-a24b-571c1ca28f14',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'Bunk Bed',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '9193515a-14b9-4261-860c-b118b31ee901',
                                'af2cec16-ec25-4df6-a472-e467ce7a048b': 630267,
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10am - 12pm',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7':
                                    '982.94',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '10:30AM',
                    formattedEndTime: '11:15AM',
                    travelTimeMinutes: 16,
                    travelDistanceKm: 5.278,
                },
                {
                    job: {
                        id: 2207428,
                        coords: { lat: 19.2182456, lng: 72.9620041 },
                        pincode: '400606',
                        reqDetails: {
                            id: 2207428,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-18T08:17:54.308243',
                            title: 'IKEA-MAYURESH-240818475894',
                            c_time: '2024-08-18T08:17:54.308243',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                org_id: 22,
                                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                                comment:
                                    'Dear Mayuresh Gore, Your request no:IKEA-MAYURESH-240818475894 has been registered with WIFY and will be attended shortly.',
                                end_time: '12:00PM',
                                entry_id: 2207428,
                                cust_city: 'Thane',
                                new_prvdr: '2',
                                cust_state: 'Maharashtra',
                                ip_address: null,
                                start_time: '10:00AM',
                                user_agent: null,
                                cust_line_0: '18C',
                                cust_line_1: 'Cosmos Horizon Phase II',
                                cust_line_2: 'Pokhran Road 2',
                                cust_mobile: '9833042624',
                                is_api_call: 1,
                                cust_pincode: '400606',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'Mayuresh Gore',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21',
                                notification_data: true,
                                is_customer_access: 0,
                                request_description:
                                    'Shoe Cabinet - 2 Compartments(1)',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-18T08:17:54.308243',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.2182456,
                                        lng: 72.9620041,
                                    },
                                    location_type: 'GEOMETRIC_CENTER',
                                },
                                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                                '0ed2668e-09d2-4feb-b091-e452713ab09f':
                                    '354.00',
                                '1906229a-b49d-4b28-927e-7c2d44971fb4':
                                    '833.00',
                                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                                '********-d80c-4169-89d5-3930f07de2d8':
                                    'pay_OmIGGj7jdO81aJ',
                                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                                    'Shoe Cabinet - 2 Compartments :\t 1 * 300 - Storage  ',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'Razor pay',
                                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                                    '939b6c5a-286a-4883-a70c-d93fbc62e73c',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'Shoe Cabinet - 2 Compartments',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '9193515a-14b9-4261-860c-b118b31ee901',
                                'af2cec16-ec25-4df6-a472-e467ce7a048b': 630267,
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10am - 12pm',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7':
                                    '982.94',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '11:30AM',
                    formattedEndTime: '12:15PM',
                    travelTimeMinutes: 0,
                    travelDistanceKm: 0,
                },
            ],
            currentTime: 600,
            currentCoords: { lat: 19.2184129, lng: 72.994773 },
            totalTravelTimeMinutes: 16,
            totalTravelDistanceKm: 5.278,
        },
    ],
    stats: [
        { text: '6/21 requests assigned', color: 'gx-text-danger' },
        { text: '5/12 users assigned', color: 'gx-text-danger' },
        { text: 'Avg. 1.20 tasks/User', color: 'gx-text-danger' },
        { text: 'Avg. travel time - 0.33 hr', color: 'gx-text-danger' },
        { text: '5 users no pincodes', color: 'gx-text-danger' },
    ],
    unassignedTechnicians: [
        {
            id: '4b2eb733-2a1d-4f34-bd84-36927954be4f',
            name: 'BOMM14241123-Guddu Bahadur',
            homeCoords: { lat: 19.0269012, lng: 72.8553773 },
            interestPincodes: '400019, 400012, 400013, 400005, 400008',
            schedule: [],
            currentTime: 600,
            currentCoords: { lat: 19.0269012, lng: 72.8553773 },
            totalTravelTimeMinutes: 0,
            totalTravelDistanceKm: 0,
        },
        {
            id: 'c7d66ad5-2f9b-4089-8b3f-3828889f4098',
            name: 'BOMM38200277-Chandrakant Kunde',
            homeCoords: { lat: 19.0521122, lng: 72.900668 },
            interestPincodes:
                '400071,400093,400074,400037,400077,400088,400089,400705,400706,4000703',
            schedule: [],
            currentTime: 600,
            currentCoords: { lat: 19.0521122, lng: 72.900668 },
            totalTravelTimeMinutes: 0,
            totalTravelDistanceKm: 0,
        },
    ],
    unassignedRequests: [
        {
            id: 2207099,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-18T07:26:29.829929',
            title: 'IKEA-HARDIK-240818913316',
            c_time: '2024-08-18T07:26:29.829929',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Hardik Joshipura , Your request no:IKEA-HARDIK-240818913316 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2207099,
                cust_city: 'Pune',
                new_prvdr: '2',
                cust_state: 'Maharashtra',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: 'D101',
                cust_line_1: "24K Opula, New DP Rd, beside WATER'S EDGE",
                cust_line_2: 'Vishal Nagar',
                cust_mobile: '9980847780',
                is_api_call: 1,
                cust_pincode: '411027',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Hardik Joshipura ',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: 'HAUGA Sideboard(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-18T07:26:29.829929',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 18.579863, lng: 73.7850996 },
                    location_type: 'ROOFTOP',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '708.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    'HAUGA Sideboard :\t 1 * 600 - Storage  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    'f1bf2912-00f4-4ccd-87d2-088e3f1060d0',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': 'HAUGA Sideboard',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 912457,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '708',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [112, 35, 95, 114, 134],
        },
        {
            id: 2209235,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-18T15:09:55.640316',
            title: 'IKEA-RAMESH-240818719389',
            c_time: '2024-08-18T15:09:55.640316',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear RAMESH KUMAR HK, Your request no:IKEA-RAMESH-240818719389 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2209235,
                cust_city: 'Bangalore',
                new_prvdr: '2',
                cust_state: 'KARNATAKA',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: 'C212 2nd floor',
                cust_line_1: 'Gopalan Brindavan Apartments',
                cust_line_2: 'Amarajyothi Nagar',
                cust_mobile: '9678048147',
                is_api_call: 1,
                cust_pincode: '560079',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'RAMESH KUMAR HK',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: '6 Drawer Chest(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-18T15:09:55.640316',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.9748445, lng: 77.5276702 },
                    location_type: 'ROOFTOP',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '531.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    '6 Drawer Chest :\t 1 * 450 - Drawers  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    'e9ccd2d6-8261-4ace-a554-b111136a4030',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': '6 Drawer Chest',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 377042,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '531',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2209543,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-18T18:29:40.614974',
            title: 'IKEA-GOPINATH-240818700982',
            c_time: '2024-08-18T18:29:40.614974',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Gopinath Rao, Your request no:IKEA-GOPINATH-240818700982 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2209543,
                cust_city: 'Bengaluru',
                new_prvdr: '2',
                cust_state: 'Karnataka',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: 'B 401',
                cust_line_1: 'Gopalan Florenza',
                cust_line_2: 'Banashankari 6th Stage',
                cust_mobile: '9620875927',
                is_api_call: 1,
                cust_pincode: '560060',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Gopinath Rao',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: '3 Seat Sofa(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-18T18:29:40.614974',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.8991363, lng: 77.51079659999999 },
                    location_type: 'ROOFTOP',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '647.82',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    '3 Seat Sofa :\t 1 * 549 - Seating  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    '26191d48-c082-4f82-b99d-d4aa5b360a1e',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': '3 Seat Sofa',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 424941,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1532.82',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2209544,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-18T18:29:40.614974',
            title: 'IKEA-GOPINATH-240818844591',
            c_time: '2024-08-18T18:29:40.614974',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Gopinath Rao, Your request no:IKEA-GOPINATH-240818844591 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2209544,
                cust_city: 'Bengaluru',
                new_prvdr: '2',
                cust_state: 'Karnataka',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: 'B 401',
                cust_line_1: 'Gopalan Florenza',
                cust_line_2: 'Banashankari 6th Stage',
                cust_mobile: '9620875927',
                is_api_call: 1,
                cust_pincode: '560060',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Gopinath Rao',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: 'Dining Table with 6 Chairs(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-18T18:29:40.614974',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.8991363, lng: 77.51079659999999 },
                    location_type: 'ROOFTOP',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '885.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    'Dining Table with 6 Chairs :\t 1 * 750 - Dining  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    '3d4ea1ed-1cd3-499d-95f5-f04ca1151d3a',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                    'Dining Table with 6 Chairs',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 424941,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1532.82',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2212116,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T08:08:23.510186',
            title: 'IKEA-DEEPIKA-240819592476',
            c_time: '2024-08-19T08:08:23.510186',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Deepika Jangid, Your request no:IKEA-DEEPIKA-240819592476 has been registered with WIFY and will be attended shortly.',
                end_time: '4:00PM',
                entry_id: 2212116,
                cust_city: 'Bangalore',
                new_prvdr: '2',
                cust_state: 'KARNATAKA',
                ip_address: null,
                start_time: '2:00PM',
                user_agent: null,
                cust_line_0: '3rd floor ',
                cust_line_1: '199',
                cust_line_2: '2nd cross 1st block Jakkasandra extensions',
                cust_mobile: '9887177224',
                is_api_call: 1,
                cust_pincode: '560034',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Deepika Jangid',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: 'Three Door Solitary Wardrobe(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T08:08:23.510186',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.9266108, lng: 77.6399624 },
                    location_type: 'ROOFTOP',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '885.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    'Three Door Solitary Wardrobe :\t 1 * 750 - Non Customizable Wardrobe  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    '873c7a58-6324-46a1-b2ae-aab00de626f4',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                    'Three Door Solitary Wardrobe',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 789526,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '2pm - 4pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '885',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2212834,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T10:17:26.79113',
            title: 'IKEA-SUHAS-240819869394',
            c_time: '2024-08-19T10:17:26.79113',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Suhas Acharya, Your request no:IKEA-SUHAS-240819869394 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2212834,
                cust_city: 'Mumbai',
                new_prvdr: '2',
                cust_state: 'Maharashtra',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: '601',
                cust_line_1: 'Ashishwang Apartments',
                cust_line_2: '72 Pochkhanwalla road, worli',
                cust_mobile: '9870555356',
                is_api_call: 1,
                cust_pincode: '400030',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Suhas Acharya',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: '3 Seat Sofa Bed(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T10:17:26.79113',
                sp_srvc_type_his_db_id: '4668',
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '826.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': '1127.00',
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': 'pay_Omiq3gR3PuVPS2',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    '3 Seat Sofa Bed :\t 1 * 700 - Seating  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'Razor pay',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    'c33128de-34cc-4d2d-8da7-154c3127bcc3',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': '3 Seat Sofa Bed',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '9193515a-14b9-4261-860c-b118b31ee901',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 770447,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1329.86',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [35, 129, 95, 114, 134, 211, 101],
        },
        {
            id: 2212835,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T10:17:26.79113',
            title: 'IKEA-SUHAS-240819990795',
            c_time: '2024-08-19T10:17:26.79113',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Suhas Acharya, Your request no:IKEA-SUHAS-240819990795 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2212835,
                cust_city: 'Mumbai',
                new_prvdr: '2',
                cust_state: 'Maharashtra',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: '601',
                cust_line_1: 'Ashishwang Apartments',
                cust_line_2: '72 Pochkhanwalla road, worli',
                cust_mobile: '9870555356',
                is_api_call: 1,
                cust_pincode: '400030',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Suhas Acharya',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: '2 Seat Sofa(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T10:17:26.79113',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 19.0038674, lng: 72.8151507 },
                    location_type: 'ROOFTOP',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '531.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': '1127.00',
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': 'pay_Omiq3gR3PuVPS2',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    '2 Seat Sofa :\t 1 * 450 - Seating  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'Razor pay',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    'e2b71d1f-e5d6-4d1e-a892-a83d9d2eca1e',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': '2 Seat Sofa',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '9193515a-14b9-4261-860c-b118b31ee901',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 770447,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1329.86',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [35, 129, 95, 114, 134, 211, 101],
        },
        {
            id: 2213095,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T11:02:57.350219',
            title: 'IKEA-SANTHOSH-240819356189',
            c_time: '2024-08-19T11:02:57.350219',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                host_d: 'tms.wify.co.in',
                org_id: 22,
                usr_id: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                comment:
                    'Dear Santhosh, Your request no:IKEA-SANTHOSH-240819356189 has been registered with WIFY and will be attended shortly.',
                entry_id: 2213095,
                cust_city: 'Bangalore Rural',
                mic_files: {},
                new_prvdr: '2',
                cust_state: 'KARNATAKA',
                ip_address: '**************:61289',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                attachments: {},
                cust_line_2: '116Mb, Surya Elegance, Bangalore 560099',
                cust_mobile: '8050987491',
                camera_files: {},
                cust_pincode: '560099',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Santhosh',
                request_priority: 'Normal',
                request_req_date: '2024-08-21T00:00:00.000Z',
                notification_data: true,
                is_customer_access: 0,
                request_description: 'Cloth & hat Rack (1) Shelving unit (1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T11:02:57.350219',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.7888125, lng: 77.7056406 },
                    location_type: 'GEOMETRIC_CENTER',
                },
                lastChangedFileSectionIds: [],
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '1062',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                    'Cloth & hat Rack (1) Shelving unit (1)',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10 am - 12 pm ',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1062',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2213253,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T11:27:57.149537',
            title: 'IKEA-SRIPRAKASH-240819160616',
            c_time: '2024-08-19T11:27:57.149537',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                host_d: 'tms.wify.co.in',
                org_id: 22,
                usr_id: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                comment:
                    'Dear Sriprakash Mayasandra , Your request no:IKEA-SRIPRAKASH-240819160616 has been registered with WIFY and will be attended shortly.',
                entry_id: 2213253,
                cust_city: 'Pune',
                mic_files: {},
                new_prvdr: '2',
                cust_state: 'MAHARASHTRA',
                ip_address: '**************:61209',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                attachments: {},
                cust_line_2: 'B1/28 KUBERA COLONY  NIBM rd ',
                cust_line_3: 'Pune 411048',
                cust_mobile: '9049046462',
                camera_files: {},
                cust_pincode: '411048',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Sriprakash Mayasandra ',
                request_priority: 'Normal',
                request_req_date: '2024-08-21T00:00:00.000Z',
                notification_data: true,
                is_customer_access: 0,
                update_for_comment: false,
                request_description: 'Shelving Unit (1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T11:27:57.149537',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 18.4772772, lng: 73.8954263 },
                    location_type: 'GEOMETRIC_CENTER',
                },
                lastChangedFileSectionIds: [],
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '590',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': 'Shelving Unit (1)',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10 am - 12 pm ',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '590',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [112, 35, 95, 114, 134],
        },
        {
            id: 2214187,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T15:04:48.123082',
            title: 'IKEA-SHALINI-240819209277',
            c_time: '2024-08-19T15:04:48.123082',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Shalini Shaji, Your request no:IKEA-SHALINI-240819209277 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2214187,
                cust_city: 'Bangalore',
                new_prvdr: '2',
                cust_state: 'KARNATAKA',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: '2092, 9th Floor ',
                cust_line_1: 'Tower 2',
                cust_line_2: 'Gummanahalli',
                cust_mobile: '7829370036',
                is_api_call: 1,
                cust_pincode: '562149',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Shalini Shaji',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: 'Double Bed(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T15:04:48.123082',
                sp_srvc_type_his_db_id: '4668',
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '708.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    'Double Bed :\t 1 * 600 - Beds  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    'cab14e68-9f96-41a9-b3b4-a94fc4e8a487',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': 'Double Bed',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 131619,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1534',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [114, 43, 128, 77, 99],
        },
        {
            id: 2214188,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-19T15:04:48.123082',
            title: 'IKEA-SHALINI-240819348688',
            c_time: '2024-08-19T15:04:48.123082',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Shalini Shaji, Your request no:IKEA-SHALINI-240819348688 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2214188,
                cust_city: 'Bangalore',
                new_prvdr: '2',
                cust_state: 'KARNATAKA',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: '2092, 9th Floor ',
                cust_line_1: 'Tower 2',
                cust_line_2: 'Gummanahalli',
                cust_mobile: '7829370036',
                is_api_call: 1,
                cust_pincode: '562149',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Shalini Shaji',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: '3 Seat Sofa Bed(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-19T15:04:48.123082',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 13.1392621, lng: 77.6835725 },
                    location_type: 'APPROXIMATE',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '826.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    '3 Seat Sofa Bed :\t 1 * 700 - Seating  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    'dd8e2d7a-3105-450b-95d0-e0c8bfbf4904',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': '3 Seat Sofa Bed',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 131619,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '1534',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [114, 43, 128, 77, 99],
        },
        {
            id: 2219183,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-20T08:56:57.4607',
            title: 'IKEA-SHESH-240820537294',
            c_time: '2024-08-20T08:56:57.4607',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Shesh Kumar. M, Your request no:IKEA-SHESH-240820537294 has been registered with WIFY and will be attended shortly.',
                end_time: '6:00PM',
                entry_id: 2219183,
                cust_city: 'Doddakalsandra, Kanakapura Road, Bangalore',
                new_prvdr: '2',
                cust_state: 'Karnataka',
                ip_address: null,
                start_time: '4:00PM',
                user_agent: null,
                cust_line_0: '2nd Floor',
                cust_line_1: 'No-14',
                cust_line_2: '4th Main Road, Kuvempu Nagar Main Road',
                cust_mobile: '9741815584',
                is_api_call: 1,
                cust_pincode: '560062',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Shesh Kumar. M',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: 'Children Desk(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-20T08:56:57.4607',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.8863716, lng: 77.5555236 },
                    location_type: 'GEOMETRIC_CENTER',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '472.00',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    'Children Desk :\t 1 * 400 - Children  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    '16a2ff5c-dd6c-4b08-bbba-e144b0b675c1',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': 'Children Desk',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 485823,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '4pm - 6pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '765.82',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2219184,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-20T08:56:57.4607',
            title: 'IKEA-SHESH-240820660536',
            c_time: '2024-08-20T08:56:57.4607',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Shesh Kumar. M, Your request no:IKEA-SHESH-240820660536 has been registered with WIFY and will be attended shortly.',
                end_time: '6:00PM',
                entry_id: 2219184,
                cust_city: 'Doddakalsandra, Kanakapura Road, Bangalore',
                new_prvdr: '2',
                cust_state: 'Karnataka',
                ip_address: null,
                start_time: '4:00PM',
                user_agent: null,
                cust_line_0: '2nd Floor',
                cust_line_1: 'No-14',
                cust_line_2: '4th Main Road, Kuvempu Nagar Main Road',
                cust_mobile: '9741815584',
                is_api_call: 1,
                cust_pincode: '560062',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Shesh Kumar. M',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: "Children's Chair(1)",
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-20T08:56:57.4607',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 12.8863716, lng: 77.5555236 },
                    location_type: 'GEOMETRIC_CENTER',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '293.82',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    "Children's Chair :\t 1 * 249 - Seating  ",
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    '86bd46a1-555d-495b-9b4c-3dbab0d3c603',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': "Children's Chair",
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 485823,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '4pm - 6pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '765.82',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2219366,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-20T09:23:27.740031',
            title: 'IKEA-SOUNDARYA-240820750469',
            c_time: '2024-08-20T09:23:27.740031',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                host_d: 'tms.wify.co.in',
                org_id: 22,
                usr_id: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                comment:
                    'Dear Soundarya Krishnan, Your request no:IKEA-SOUNDARYA-240820750469 has been registered with WIFY and will be attended shortly.',
                entry_id: 2219366,
                cust_city: 'Bangalore Rural',
                mic_files: {},
                new_prvdr: '2',
                cust_state: 'KARNATAKA',
                ip_address: '*************:59633',
                user_agent:
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                attachments: {},
                cust_line_2:
                    'villa A30 Sterling villa grande, Whitefield hoskote road',
                cust_line_3: 'seegehalli 560067 Bengaluru',
                cust_mobile: '9845716397',
                camera_files: {},
                cust_pincode: '560067',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Soundarya Krishnan',
                request_priority: 'Normal',
                request_req_date: '2024-08-21T00:00:00.000Z',
                notification_data: true,
                is_customer_access: 0,
                request_description:
                    'Wing Chair (1) \nOffice Chair (1) \n3 Seat Sofa (1)\nPoang Armchair (1)\nRocking Chair (1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-20T09:23:27.740031',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 13.0113086, lng: 77.7596159 },
                    location_type: 'GEOMETRIC_CENTER',
                },
                lastChangedFileSectionIds: [],
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '2183',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                    'Wing Chair (1)  Office Chair (1)  3 Seat Sofa (1) Poang Armchair (1) Rocking Chair (1)',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 5,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10 am - 12 pm ',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '2183',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
        {
            id: 2219422,
            org: {
                label: 'IKEA_B2C',
                value: 22,
                icon_path: 'https://home.wify.co.in/favicon.ico',
            },
            time: '2024-08-20T09:32:00.466615',
            title: 'IKEA-AKSHAY-240820549527',
            c_time: '2024-08-20T09:32:00.466615',
            status: {
                key: 'open',
                color: '#e91e63',
                title: 'Open',
                status_type: 'ACTIVE',
            },
            priority: 'Normal',
            form_data: {
                title: 'Whatsapp notification sent to customer',
                org_id: 22,
                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                comment:
                    'Dear Akshay Lodaya, Your request no:IKEA-AKSHAY-240820549527 has been registered with WIFY and will be attended shortly.',
                end_time: '12:00PM',
                entry_id: 2219422,
                cust_city: 'Bengaluru',
                new_prvdr: '2',
                cust_state: 'Karnataka',
                ip_address: null,
                start_time: '10:00AM',
                user_agent: null,
                cust_line_0: 'C-001',
                cust_line_1: 'BAIRAVI CRUZ LUXOR',
                cust_line_2: 'HRBR Layout',
                cust_mobile: '9845202358',
                is_api_call: 1,
                cust_pincode: '560043',
                srvc_type_id: '41',
                authority_1214: 'fb99329e-3c95-47ef-94a6-c5899312e73d',
                cust_full_name: 'Akshay Lodaya',
                request_priority: 'Normal',
                request_req_date: '2024-08-21',
                notification_data: true,
                is_customer_access: 0,
                request_description: '3 Seat Sofa(1)',
                srvc_type_his_db_id: '6553',
                prvdr_assg_timestamp: '2024-08-20T09:32:00.466615',
                sp_srvc_type_his_db_id: '4668',
                geocoding_location_data: {
                    location: { lat: 13.0273978, lng: 77.6380409 },
                    location_type: 'GEOMETRIC_CENTER',
                },
                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                '0ed2668e-09d2-4feb-b091-e452713ab09f': '647.82',
                '1906229a-b49d-4b28-927e-7c2d44971fb4': null,
                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                '********-d80c-4169-89d5-3930f07de2d8': '',
                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                    '3 Seat Sofa :\t 1 * 549 - Seating  ',
                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a': 'COD Payment',
                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                    '39e5e3c0-381c-4150-a137-6c75a76e1700',
                '7c9c1ac7-e11d-44a9-9634-616baba018b7': '3 Seat Sofa',
                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                    '63b0db07-419e-49bf-a416-bfab39b22445',
                'af2cec16-ec25-4df6-a472-e467ce7a048b': 152053,
                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6': '10am - 12pm',
                'fb1ca71d-6822-4577-8be2-61fc5c483cc7': '647.82',
            },
            is_deleted: false,
            srvc_type_id: 41,
            brand_location_groups: [219],
            prvdr_location_groups: [94, 114, 43, 128, 77, 99],
        },
    ],
    startHour: 10,
    endHour: 18,
};
const mockGenAssignmentAPI = (resp = mockGenAssignmentResp) => {
    mockPostCall(({ params, onComplete }) => {
        // console.info('API called');
        onComplete({
            data: {
                body: JSON.stringify(resp),
            },
        });
    });
};
const mockGantt = () => {
    GanttCalendar.mockImplementation((props) => {
        const { items, customItemRender } = props;
        const transformedItems = [];
        items.map((singleItem) => {
            singleItem.jobs.map((singleJob) => {
                transformedItems.push(singleJob);
            });
        });
        return (
            <div>
                {transformedItems.map((singleItem) => {
                    return customItemRender('', singleItem);
                })}
            </div>
        );
    });
};
// Test whether delete button is rendered on each item
test(`${prefix} delete item with warning`, async () => {
    mockGenAssignmentAPI();
    mockGantt();
    render(
        <BrowserRouter>
            <AutoAssignProgress
                sbtskData={{
                    label: 'Test type 1',
                    value: '1',
                }}
            />
        </BrowserRouter>
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));
    // Find the element that triggers the Popover
    const singleJobWrappers =
        await screen.findAllByTestId('single-job-wrapper');
    expect(singleJobWrappers.length).toBe(6); // as per mock data

    // Simulate hovering over the element to trigger the Popover
    act(() => {
        userEvent.hover(singleJobWrappers[0]);
    });
    // Below Code is commented as the delete button has moved inside the popover
    const deleteButtons = await screen.findAllByTestId('delete-job-btn');
    expect(deleteButtons.length).toBe(1);
    act(() => {
        userEvent.click(deleteButtons[0]);
    });
    // The popconfirm should be visible with the title text
    const confirmText = await screen.findByText(
        /Are you sure you want to remove/
    );
    expect(confirmText).toBeInTheDocument();

    // Click the "OK" button in the Popconfirm
    const okButton = screen.getByRole('button', { name: /ok/i });
    expect(okButton).toBeInTheDocument();
    act(() => {
        userEvent.click(okButton);
    });
    // screen.debug();
    const newSingleJobWrappers = screen.getAllByTestId('single-job-wrapper');
    expect(newSingleJobWrappers.length).toBe(5); // as 1 should be deleted

    const infoText = await screen.findByText(/Draft is manually modified/);
    expect(infoText).toBeInTheDocument();

    // Regenerate click removes infoText
    const regenerateButton = screen.getByRole('button', {
        name: /Regenerate/i,
    });
    expect(regenerateButton).toBeInTheDocument();
    act(() => {
        userEvent.click(regenerateButton);
    });
    // Set a timeout for 2 seconds to simulate the delay
    // await new Promise((resolve) => setTimeout(resolve, 3000));
    const infoTextAfterRegenerate = screen.queryByText(
        /Draft is manually modified/
    );
    expect(infoTextAfterRegenerate).not.toBeInTheDocument();
});
// Test whether on hove the title of ticket comes within a antd link object and sends me to srvcprvdr if configIsPrvdrReturns true(mocking it)
test(`${prefix} Ticket title click redirect to /services`, async () => {
    mockGenAssignmentAPI();
    mockGantt();
    // Mock the specific method
    const mockIsSrvcPrvdrBySrvcReqId = jest
        .spyOn(ConfigHelpers, 'isSrvcPrvdrBySrvcReqId')
        .mockReturnValue(false);
    render(
        <BrowserRouter>
            <AutoAssignProgress
                sbtskData={{
                    label: 'Test type 1',
                    value: '1',
                }}
            />
        </BrowserRouter>
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const singleJobWrappers =
        await screen.findAllByTestId('single-job-wrapper');
    expect(singleJobWrappers.length).toBe(6);
    act(() => {
        userEvent.hover(singleJobWrappers[0]);
    });
    // await new Promise((resolve) => setTimeout(resolve, 1000));
    const titleLink = await screen.findAllByRole('link', {
        name: mockGenAssignmentResp.calendarData[0].schedule[0].job.reqDetails
            .title,
    });
    // screen.debug(undefined, Infinity);
    expect(titleLink.length).toBe(1);
    // Confirm the link is rendered and interactable
    expect(titleLink[0]).toBeInTheDocument();
    expect(titleLink[0]).toBeEnabled(); // Ensures the link is not disabled
    expect(titleLink[0]).toHaveAttribute(
        'href',
        expect.stringMatching(/\/services\//)
    );
    expect(titleLink[0]).toHaveAttribute('target', '_blank');
    // Restore the original method after the test
    mockIsSrvcPrvdrBySrvcReqId.mockRestore();
});
test(`${prefix} Ticket title click redirect to /customer`, async () => {
    mockGenAssignmentAPI();
    mockGantt();
    // Mock the specific method
    const mockIsSrvcPrvdrBySrvcReqId = jest
        .spyOn(ConfigHelpers, 'isSrvcPrvdrBySrvcReqId')
        .mockReturnValue(true);
    render(
        <BrowserRouter initialEntries={['/']}>
            <AutoAssignProgress
                sbtskData={{
                    label: 'Test type 1',
                    value: '1',
                }}
            />
        </BrowserRouter>
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const singleJobWrappers =
        await screen.findAllByTestId('single-job-wrapper');
    expect(singleJobWrappers.length).toBe(6);
    act(() => {
        userEvent.hover(singleJobWrappers[0]);
    });
    // await new Promise((resolve) => setTimeout(resolve, 1000));
    const titleLink = await screen.findAllByRole('link', {
        name: mockGenAssignmentResp.calendarData[0].schedule[0].job.reqDetails
            .title,
    });
    // screen.debug(undefined, Infinity);
    expect(titleLink.length).toBe(1);
    // Confirm the link is rendered and interactable
    expect(titleLink[0]).toBeInTheDocument();
    expect(titleLink[0]).toBeEnabled(); // Ensures the link is not disabled
    expect(titleLink[0]).toHaveAttribute(
        'href',
        expect.stringMatching(/\/customer-requests/)
    );
    expect(titleLink[0]).toHaveAttribute('target', '_blank');
    // Restore the original method after the test
    mockIsSrvcPrvdrBySrvcReqId.mockRestore();
});
test(`${prefix} shows start_time`, async () => {
    mockGenAssignmentAPI();
    mockGantt();
    render(
        <BrowserRouter initialEntries={['/']}>
            <AutoAssignProgress
                sbtskData={{
                    label: 'Test type 1',
                    value: '1',
                }}
            />
        </BrowserRouter>
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const singleJobWrappers =
        await screen.findAllByTestId('single-job-wrapper');
    expect(singleJobWrappers.length).toBe(6);
    act(() => {
        userEvent.hover(singleJobWrappers[0]);
    });
    // await new Promise((resolve) => setTimeout(resolve, 1000));
    // expect 10:00AM written somewhere
    await screen.findByText(/10:00AM/);

    // hover on 3rd order
    act(() => {
        userEvent.hover(singleJobWrappers[2]);
    });

    // make sure mock data is correct
    const startTimeText2 = await screen.findAllByText(
        /IKEA-S.RAJGOPAL-240820528022/,
        'link'
    );
    // make sure there is no text in document saying requested time -
    const requestedStartTimeLabel = screen.queryAllByText(/Requested time/);
    expect(requestedStartTimeLabel.length).toBe(1);
});

const mockGenAssignmentRespWithExistingBlocks = {
    calendarData: [
        {
            id: '3f3bda47-a247-4f21-a221-e6d62d25aae8',
            name: 'TNAM15200462-Sunil Gupta',
            homeCoords: { lat: 19.2184129, lng: 72.994773 },
            interestPincodes:
                '400601,400605,400612,400601,400602,400603,400604,400606,400607,400608,400609,400610,400615,400617,400105,401016,401100,401101,401104,401105,401106,401107,401200,401400,401406,400601,400601,400603,400605,400612,400706,400709,410101,412208,421001,421102,421201,421201,421202,421203,421204,421205,421300,421301,421304,421306,421001,421002,421003,421004,421005,421501,421502,421503,421505,421506,421102,421103,421401,421402,421403,421603,421605,421101,421302,421303,421305,421308,421311,421312,421601,421604,421203,421501,421502,421506\n\n,421503,421605,425409',
            schedule: [
                {
                    job: {
                        id: 2207427,
                        coords: { lat: 19.2182456, lng: 72.9620041 },
                        pincode: '400606',
                        reqDetails: {
                            id: 2207427,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-18T08:17:54.308243',
                            title: 'IKEA-MAYURESH-240818382232',
                            c_time: '2024-08-18T08:17:54.308243',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                org_id: 22,
                                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                                comment:
                                    'Dear Mayuresh Gore, Your request no:IKEA-MAYURESH-240818382232 has been registered with WIFY and will be attended shortly.',
                                end_time: '12:00PM',
                                entry_id: 2207427,
                                cust_city: 'Thane',
                                new_prvdr: '2',
                                cust_state: 'Maharashtra',
                                ip_address: null,
                                start_time: '10:00AM',
                                user_agent: null,
                                cust_line_0: '18C',
                                cust_line_1: 'Cosmos Horizon Phase II',
                                cust_line_2: 'Pokhran Road 2',
                                cust_mobile: '9833042624',
                                is_api_call: 1,
                                cust_pincode: '400606',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'Mayuresh Gore',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21',
                                notification_data: true,
                                is_customer_access: 0,
                                request_description: 'Bunk Bed(1)',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-18T08:17:54.308243',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.2182456,
                                        lng: 72.9620041,
                                    },
                                    location_type: 'GEOMETRIC_CENTER',
                                },
                                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                                '0ed2668e-09d2-4feb-b091-e452713ab09f':
                                    '649.00',
                                '1906229a-b49d-4b28-927e-7c2d44971fb4':
                                    '833.00',
                                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                                '********-d80c-4169-89d5-3930f07de2d8':
                                    'pay_OmIGGj7jdO81aJ',
                                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                                    'Bunk Bed :\t 1 * 550 - Beds  ',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'Razor pay',
                                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                                    'bc858215-0475-48e2-a24b-571c1ca28f14',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'Bunk Bed',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '9193515a-14b9-4261-860c-b118b31ee901',
                                'af2cec16-ec25-4df6-a472-e467ce7a048b': 630267,
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10am - 12pm',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7':
                                    '982.94',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '10:30AM',
                    formattedEndTime: '11:15AM',
                    travelTimeMinutes: 16,
                    travelDistanceKm: 5.278,
                },
                {
                    job: {
                        id: 2207428,
                        coords: { lat: 19.2182456, lng: 72.9620041 },
                        pincode: '400606',
                        reqDetails: {
                            id: 2207428,
                            org: {
                                label: 'IKEA_B2C',
                                value: 22,
                                icon_path:
                                    'https://home.wify.co.in/favicon.ico',
                            },
                            time: '2024-08-18T08:17:54.308243',
                            title: 'IKEA-MAYURESH-240818475894',
                            c_time: '2024-08-18T08:17:54.308243',
                            status: {
                                key: 'open',
                                color: '#e91e63',
                                title: 'Open',
                                status_type: 'ACTIVE',
                            },
                            priority: 'Normal',
                            form_data: {
                                title: 'Whatsapp notification sent to customer',
                                org_id: 22,
                                usr_id: '797ef484-fa6d-4ee9-91ae-6040094dd615',
                                comment:
                                    'Dear Mayuresh Gore, Your request no:IKEA-MAYURESH-240818475894 has been registered with WIFY and will be attended shortly.',
                                end_time: '12:00PM',
                                entry_id: 2207428,
                                cust_city: 'Thane',
                                new_prvdr: '2',
                                cust_state: 'Maharashtra',
                                ip_address: null,
                                start_time: '10:00AM',
                                user_agent: null,
                                cust_line_0: '18C',
                                cust_line_1: 'Cosmos Horizon Phase II',
                                cust_line_2: 'Pokhran Road 2',
                                cust_mobile: '9833042624',
                                is_api_call: 1,
                                cust_pincode: '400606',
                                srvc_type_id: '41',
                                authority_1214:
                                    'fb99329e-3c95-47ef-94a6-c5899312e73d',
                                cust_full_name: 'Mayuresh Gore',
                                request_priority: 'Normal',
                                request_req_date: '2024-08-21',
                                notification_data: true,
                                is_customer_access: 0,
                                request_description:
                                    'Shoe Cabinet - 2 Compartments(1)',
                                srvc_type_his_db_id: '6553',
                                prvdr_assg_timestamp:
                                    '2024-08-18T08:17:54.308243',
                                sp_srvc_type_his_db_id: '4668',
                                geocoding_location_data: {
                                    location: {
                                        lat: 19.2182456,
                                        lng: 72.9620041,
                                    },
                                    location_type: 'GEOMETRIC_CENTER',
                                },
                                '00212a5c-80ed-4caa-8f1f-1ebb6a86d0e3': null,
                                '0ed2668e-09d2-4feb-b091-e452713ab09f':
                                    '354.00',
                                '1906229a-b49d-4b28-927e-7c2d44971fb4':
                                    '833.00',
                                '484d6704-b9a4-47eb-9247-70afc78efdb2': null,
                                '********-d80c-4169-89d5-3930f07de2d8':
                                    'pay_OmIGGj7jdO81aJ',
                                '61b8daee-6e99-4c07-a44a-621b32a427d3':
                                    'Shoe Cabinet - 2 Compartments :\t 1 * 300 - Storage  ',
                                '6cbe44b1-a869-4f37-a5ad-d2e951a1b09a':
                                    'Razor pay',
                                '79a88c7b-c64f-46c4-a277-bc80efa1c154':
                                    '939b6c5a-286a-4883-a70c-d93fbc62e73c',
                                '7c9c1ac7-e11d-44a9-9634-616baba018b7':
                                    'Shoe Cabinet - 2 Compartments',
                                '7e87b729-f6f7-434d-9407-b4a2cc0aea82':
                                    '9193515a-14b9-4261-860c-b118b31ee901',
                                'af2cec16-ec25-4df6-a472-e467ce7a048b': 630267,
                                'e09088dc-e3e6-4eb9-86c5-cf07c3a01779': 1,
                                'f4c9c83f-3265-4c88-901f-bd6b5e0154a6':
                                    '10am - 12pm',
                                'fb1ca71d-6822-4577-8be2-61fc5c483cc7':
                                    '982.94',
                            },
                            is_deleted: false,
                            srvc_type_id: 41,
                            brand_location_groups: [219],
                            prvdr_location_groups: [
                                35, 129, 95, 114, 134, 211, 101,
                            ],
                        },
                        assigned: true,
                    },
                    formattedStartTime: '11:30AM',
                    formattedEndTime: '12:15PM',
                    travelTimeMinutes: 0,
                    travelDistanceKm: 0,
                    isExistingAlready: true,
                },
            ],
            currentTime: 600,
            currentCoords: { lat: 19.2184129, lng: 72.994773 },
            totalTravelTimeMinutes: 16,
            totalTravelDistanceKm: 5.278,
        },
    ],
    stats: [
        { text: '6/21 requests assigned', color: 'gx-text-danger' },
        { text: '5/12 users assigned', color: 'gx-text-danger' },
        { text: 'Avg. 1.20 tasks/User', color: 'gx-text-danger' },
        { text: 'Avg. travel time - 0.33 hr', color: 'gx-text-danger' },
        { text: '5 users no pincodes', color: 'gx-text-danger' },
    ],
    startHour: 10,
    endHour: 18,
};
// Shows existing tasks in grey color and no delete button
test(`${prefix} Shows existing tasks in grey color and no delete button`, async () => {
    mockGenAssignmentAPI(mockGenAssignmentRespWithExistingBlocks);
    mockGantt();
    render(
        <BrowserRouter initialEntries={['/']}>
            <AutoAssignProgress
                sbtskData={{
                    label: 'Test type 1',
                    value: '1',
                }}
            />
        </BrowserRouter>
    );

    // Set a timeout for 2 seconds to simulate the delay
    await new Promise((resolve) => setTimeout(resolve, animationDelay));

    const singleJobWrappers =
        await screen.findAllByTestId('single-job-wrapper');
    expect(singleJobWrappers.length).toBe(2);
    // Check if 2nd wrapper has grey background color
    expect(singleJobWrappers[1]).toHaveStyle(
        'background-color: rgb(97, 97, 97)'
    );
    // Check 1st wrapper has blue bg color
    expect(singleJobWrappers[0]).toHaveStyle(
        'background-color: rgb(121, 129, 176)'
    );
    // Hover over 2nd task
    await act(async () => {
        userEvent.hover(singleJobWrappers[1]);
    });
    // popover delay
    await new Promise((resolve) => setTimeout(resolve, 100));
    const deleteButtons = screen.queryAllByTestId('delete-job-btn');
    expect(deleteButtons.length).toBe(0);
});
