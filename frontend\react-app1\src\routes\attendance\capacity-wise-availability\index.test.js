import React from 'react';
import { render } from '@testing-library/react';
import index from './index';
beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});
describe('Smoke Test for index', () => {
    it('renders without crashing', () => {
        render(<index />);
    });
});
