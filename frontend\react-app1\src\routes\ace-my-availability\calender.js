import React, { useState, useEffect } from 'react';
import moment from 'moment';
import { Calendar, momentLocalizer } from 'react-big-calendar';

const CalendarView = () => {
  const [selectedDate, setSelectedDate] = useState(moment());
  const [events, setEvents] = useState({});
  const [dayEvents, setDayEvents] = useState([]);
  const [loading, setLoading] = useState(false);

  const startOfMonth = selectedDate.clone().startOf('month');
  const endOfMonth = selectedDate.clone().endOf('month');
  const startDay = startOfMonth.clone().startOf('week');
  const endDay = endOfMonth.clone().endOf('week');

  const calendarDays = [];
  let day = startDay.clone();
  while (day.isBefore(endDay, 'day')) {
    calendarDays.push(day.clone());
    day.add(1, 'day');
  }

  useEffect(() => {
    const fetchMarkedDates = async () => {
      const response = await fetch('/api/calendar/days-with-events');
      const data = await response.json(); // { '2025-06-03': [...], ... }
      setEvents(data);
    };
    fetchMarkedDates();
  }, []);

  const handleDateClick = async (date) => {
    setSelectedDate(date);
    setLoading(true);
    const formatted = date.format('YYYY-MM-DD');

    try {
      const res = await fetch(`/api/calendar/events?date=${formatted}`);
      const data = await res.json();
      setDayEvents(data || []);
    } catch (err) {
      console.error('Error fetching events:', err);
      setDayEvents([]);
    } finally {
      setLoading(false);
    }
  };

  const isSameDay = (d1, d2) => d1.isSame(d2, 'day');
  const isToday = (day) => moment().isSame(day, 'day');
  const hasDot = (day) => !!events[day.format('YYYY-MM-DD')];

  return (
    <div className="p-4 max-w-md mx-auto">
      <div className="flex justify-between items-center mb-4">
        <button onClick={() => setSelectedDate(selectedDate.clone().subtract(1, 'month'))}>
          &lt;
        </button>
        <h3 className="text-lg font-semibold">{selectedDate.format('MMMM YYYY')}</h3>
        <button onClick={() => setSelectedDate(selectedDate.clone().add(1, 'month'))}>
          &gt;
        </button>
      </div>

      <div className="grid grid-cols-7 text-center text-sm font-medium mb-1">
        {['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'].map((d) => (
          <div key={d}>{d}</div>
        ))}
      </div>

      <div className="grid grid-cols-7 gap-1 text-center text-sm">
        {calendarDays.map((day, idx) => {
          const isSelected = isSameDay(day, selectedDate);
          return (
            <div
              key={idx}
              onClick={() => handleDateClick(day)}
              className={`py-2 rounded cursor-pointer relative
                ${isSelected ? 'bg-blue-500 text-white' : 'hover:bg-gray-200'}
                ${!day.isSame(selectedDate, 'month') ? 'text-gray-400' : ''}
              `}
            >
              {day.date()}
              {hasDot(day) && (
                <div className="w-1 h-1 bg-blue-500 rounded-full absolute left-1/2 bottom-1 -translate-x-1/2"></div>
              )}
            </div>
          );
        })}
      </div>

      <div className="mt-5">
        <h4 className="font-semibold">
          Events on {selectedDate.format('dddd, MMMM D')}:
        </h4>
        {loading ? (
          <p className="text-gray-500 text-sm mt-2">Loading...</p>
        ) : dayEvents.length ? (
          <ul className="mt-2 space-y-1">
            {dayEvents.map((event, i) => (
              <li key={i} className="text-sm text-gray-700">
                <span className="font-semibold">{event.time}</span>: {event.text}
              </li>
            ))}
          </ul>
        ) : (
          <p className="text-sm text-gray-500 mt-2">No events for this day.</p>
        )}
      </div>
    </div>
  );
};

export default CalendarView;
