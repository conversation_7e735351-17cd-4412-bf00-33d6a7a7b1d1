import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Spin, Divider } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import moment from 'moment';
import http_utils from '../../util/http_utils';
import { availabilityStatuses, getStatusCode } from './helpers';
import SingleDay from './SingleDay';
import './calendar.css';
import CircularProgress from '../../components/CircularProgress';

const { Text, Title } = Typography;

const statusColors = {
    success: 'success',
    warning: 'warning',
    error: 'red',
    default: 'grey',
};

const AvailabilityCalendar = () => {
    const [currentDate, setCurrentDate] = useState(moment());
    const [viewData, setViewData] = useState(null);
    const [availabilityData, setAvailabilityData] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [selectedDate, setSelectedDate] = useState(moment()); // Default to today
    const [error, setError] = useState(null);

    // Generate calendar days for the current month
    const generateCalendarDays = () => {
        const startOfMonth = currentDate.clone().startOf('month');
        const endOfMonth = currentDate.clone().endOf('month');
        const startOfWeek = startOfMonth.clone().startOf('week');
        const endOfWeek = endOfMonth.clone().endOf('week');

        const days = [];
        let day = startOfWeek.clone();

        while (day.isSameOrBefore(endOfWeek, 'day')) {
            days.push(day.clone());
            day.add(1, 'day');
        }

        return days;
    };

  

    const fetchAvailabilityData = async () => {
        setIsLoading(true);
        const startOfMonth = currentDate
            .clone()
            .startOf('month')
            .format('YYYY-MM-DD');
        const endOfMonth = currentDate
            .clone()
            .endOf('month')
            .format('YYYY-MM-DD');

        try {
            const onComplete = (resp) => {
                setIsLoading(false);
                const dateWiseData = {};
                setViewData(resp.data);
                if (resp.data && resp.data.availability) {
                    resp.data.availability.forEach((dayData) => {
                        const { date, slots = [] } = dayData;
                        const totalSlots = slots.length;
                        const availableSlots = slots.filter(
                            (slot) => slot.is_available
                        ).length;
                        dateWiseData[date] = getStatusCode(
                            totalSlots,
                            availableSlots
                        );
                    });
                }

                console.log('Availability data fetched:', dateWiseData);
                setAvailabilityData(dateWiseData);
            };

            const onError = (error) => {
                setIsLoading(false);
                console.error('Error fetching availability data:', error);
                setError(http_utils.decodeErrorToMessage(error));
                setAvailabilityData({});
            };

            // Use the date range API endpoint with correct parameter names
            http_utils.performGetCall(
                '/my-availability/range',
                {
                    start_date: startOfMonth,
                    end_date: endOfMonth,
                },
                onComplete,
                onError
            );
        } catch (error) {
            setIsLoading(false);
            setError(http_utils.decodeErrorToMessage(error));
            setAvailabilityData({});
        }
    };

    useEffect(() => {
        fetchAvailabilityData();
    }, [currentDate]);

    // Handle date click
    const handleDateClick = (day) => {
        setSelectedDate(day);

        // Optional: auto-navigate if user clicks on a day from another month
        if (!day.isSame(currentDate, 'month')) {
            setCurrentDate(day.clone().startOf('month'));
        }
    };

    // Handle month navigation
    const navigateMonth = (direction) => {
        setCurrentDate((prev) => prev.clone().add(direction, 'month'));
    };

    if (isLoading) {
        return (
            <div className="gx-loader-view gx-loader-position">
                <Spin />
            </div>
        );
    } else if (viewData == null) {
        return <p className="gx-text-red">{error}</p>;
    }

    const getDotColor = (date) => {
        const dateStr = date.format('YYYY-MM-DD');
        const status =
            availabilityData[dateStr] || availabilityStatuses.offline.value;

        if (status) {
            return (
                statusColors[availabilityStatuses[status]?.color] ||
                statusColors.default
            );
        }
    };

    const calendarDays = generateCalendarDays();
    const today = moment();

    return (
        <div className="availability-calendar">
            <Card className="availability-calendar-card-wrapper">
                {/* Calendar Header */}
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center gx-p-2">
                    <Button
                        icon={<LeftOutlined className="gx-fs-sm gx-mb-0" />}
                        onClick={() => navigateMonth(-1)}
                        type="text"
                        className="gx-mb-0"
                        size='small'
                    />
                    <Title level={5} className="gx-mb-0 gx-text-black">
                        {currentDate.format('MMMM YYYY')}
                    </Title>
                    <Button
                        icon={<RightOutlined className="gx-fs-sm gx-mb-0" />}
                        onClick={() => navigateMonth(1)}
                        type="text"
                        className="gx-mb-0"
                        size='small'
                    />
                </div>

                {/* Days of week header */}
                <div className="calendar-grid gx-mb-1">
                    {['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'].map(
                        (day) => (
                            <div
                                key={day}
                                className="gx-flex-1 gx-text-center"
                                style={{ fontSize: '12px' }}
                            >
                                {day}
                            </div>
                        )
                    )}
                </div>

                {/* Calendar Grid */}
                <div className="calendar-grid-wrapper">
                    {Array.from(
                        { length: Math.ceil(calendarDays.length / 7) },
                        (_, weekIndex) => (
                            <div key={weekIndex} className="calendar-grid">
                                {calendarDays
                                    .slice(weekIndex * 7, (weekIndex + 1) * 7)
                                    .map((day, dayIndex) => {
                                        const isCurrentMonth = day.isSame(
                                            currentDate,
                                            'month'
                                        );
                                        const isToday = day.isSame(
                                            today,
                                            'day'
                                        );
                                        const isSelected = day.isSame(
                                            selectedDate,
                                            'day'
                                        );
                                        const dotColor = getDotColor(day);

                                        return (
                                            <div
                                                key={dayIndex}
                                                className={`calendar-day gx-flex-1 gx-text-center gx-cursor-pointer gx-position-relative  gx-d-flex gx-flex-column gx-align-items-center gx-justify-content-center gx-rounded-circle gx-mx-auto calendar-day-fixed ${
                                                    isCurrentMonth
                                                        ? ''
                                                        : 'gx-text-grey'
                                                } ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''}`}
                                                onClick={() =>
                                                    handleDateClick(day)
                                                }
                                            >
                                                <Text
                                                    className={
                                                        day.month() ===
                                                        currentDate.month()
                                                            ? 'gx-text-black'
                                                            : 'gx-text-grey'
                                                    }
                                                >
                                                    {day.date()}
                                                </Text>

                                                {/* Availability dot */}
                                                {isCurrentMonth && (
                                                    <span
                                                        className={`wy-calendar-day-${dotColor} gx-rounded-circle gx-mt-1 `}
                                                        style={{
                                                            width: '5px',
                                                            height: '5px',
                                                            opacity:
                                                                availabilityData[
                                                                    day.format(
                                                                        'YYYY-MM-DD'
                                                                    )
                                                                ]
                                                                    ? 1
                                                                    : 0.3,
                                                        }}
                                                    />
                                                )}
                                            </div>
                                        );
                                    })}
                            </div>
                        )
                    )}
                </div>

                {/* Legend */}
                <div className="calendar-legend">
                    <div className="legend-item">
                        <span
                            className={`wy-calendar-day-${statusColors.success} legend-dot`}
                        />
                        <Text className="legend-label">Available</Text>
                    </div>
                    <div className="legend-item">
                        <span
                            className={`wy-calendar-day-${statusColors.warning} legend-dot`}
                        />
                        <Text className="legend-label">Limited</Text>
                    </div>
                    <div className="legend-item">
                        <span
                            className={`wy-calendar-day-${statusColors.error} legend-dot`}
                        />
                        <Text className="legend-label">Offline</Text>
                    </div>
                </div>
            </Card>

            {/* Selected Date Details */}
            {selectedDate && (
                <>
                    <div>
                        <Divider className="gx-text-primary">
                            {selectedDate.isSame(today, 'day')
                                ? `Today  ${selectedDate.format('D')}`
                                : selectedDate.format('ddd D')}
                        </Divider>
                    </div>

                    <div>
                        <SingleDay
                            day={{
                                date: selectedDate.format('YYYY-MM-DD'),
                            }}
                            onNewStatus={(statusCode) => {
                                // Update the availability data when status changes
                                setAvailabilityData((prev) => ({
                                    ...prev,
                                    [selectedDate.format('YYYY-MM-DD')]:
                                        statusCode,
                                }));
                            }}
                        />
                    </div>
                </>
            )}
        </div>
    );
};

export default AvailabilityCalendar;
