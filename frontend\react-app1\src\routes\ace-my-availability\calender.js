import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Spin, Divider } from 'antd';
import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import moment from 'moment';
import http_utils from '../../util/http_utils';
import { availabilityStatuses, getStatusCode } from './helpers';
import SingleDay from './SingleDay';
import './calendar.css';

const { Text, Title } = Typography;

const AvailabilityCalendar = () => {
    const [currentDate, setCurrentDate] = useState(moment());
    const [availabilityData, setAvailabilityData] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [selectedDate, setSelectedDate] = useState(moment()); // Default to today

    // Generate calendar days for the current month
    const generateCalendarDays = () => {
        const startOfMonth = currentDate.clone().startOf('month');
        const endOfMonth = currentDate.clone().endOf('month');
        const startOfWeek = startOfMonth.clone().startOf('week');
        const endOfWeek = endOfMonth.clone().endOf('week');

        const days = [];
        let day = startOfWeek.clone();

        while (day.isSameOrBefore(endOfWeek, 'day')) {
            days.push(day.clone());
            day.add(1, 'day');
        }

        return days;
    };

    const canUpdateDate = (date) => {
        const today = moment().startOf('day');
        return date.isSameOrAfter(today);
    };

    // Fetch availability data for the current month
    // const fetchAvailabilityData = async () => {
    //     setIsLoading(true);
    //     const startOfMonth = currentDate.clone().startOf('month');
    //     const endOfMonth = currentDate.clone().endOf('month');

    //     try {
    //         const dateWiseData = {};
    //         const promises = [];

    //         // Generate all dates in the current month
    //         let currentDay = startOfMonth.clone();
    //         while (currentDay.isSameOrBefore(endOfMonth, 'day')) {
    //             const dateStr = currentDay.format('YYYY-MM-DD');

    //             // Create a promise for each day's availability data
    //             const promise = new Promise((resolve) => {
    //                 const onComplete = (resp) => {
    //                     if (resp.data && resp.data.slots) {
    //                         const { slots = [] } = resp.data;
    //                         const totalSlots = slots.length;
    //                         const availableSlots = slots.filter(
    //                             (slot) => slot.is_available
    //                         ).length;
    //                         dateWiseData[dateStr] = getStatusCode(
    //                             totalSlots,
    //                             availableSlots
    //                         );
    //                     }
    //                     resolve();
    //                 };

    //                 const onError = (error) => {
    //                     console.error(
    //                         `Error fetching data for ${dateStr}:`,
    //                         error
    //                     );
    //                     resolve(); // Continue even if one date fails
    //                 };

    //                 // Use the existing single day API endpoint
    //                 http_utils.performGetCall(
    //                     '/my-availability/day',
    //                     { date: dateStr },
    //                     onComplete,
    //                     onError
    //                 );
    //             });

    //             promises.push(promise);
    //             currentDay.add(1, 'day');
    //         }

    //         // Wait for all API calls to complete
    //         await Promise.all(promises);
    //         console.log('Availability data fetched:', dateWiseData);
    //         setAvailabilityData(dateWiseData);
    //         setIsLoading(false);
    //     } catch (error) {
    //         setIsLoading(false);
    //         console.error('Error fetching availability data:', error);
    //         setAvailabilityData({});
    //     }
    // };

    const fetchAvailabilityData = async () => {
        setIsLoading(true);
        const startOfMonth = currentDate
            .clone()
            .startOf('month')
            .format('YYYY-MM-DD');
        const endOfMonth = currentDate
            .clone()
            .endOf('month')
            .format('YYYY-MM-DD');

        try {
            const dateWiseData = {};

            // Perform a single API call to get all data in the date range
            const response = await http_utils.performGetCall(
                '/my-availability/range',
                {
                    start: startOfMonth,
                    end: endOfMonth,
                }
            );

            if (response.data) {
                Object.entries(response.data).forEach(([dateStr, dayData]) => {
                    const slots = dayData.slots || [];
                    const totalSlots = slots.length;
                    const availableSlots = slots.filter(
                        (slot) => slot.is_available
                    ).length;
                    dateWiseData[dateStr] = getStatusCode(
                        totalSlots,
                        availableSlots
                    );
                });
            }

            console.log('Availability data fetched:', dateWiseData);
            setAvailabilityData(dateWiseData);
        } catch (error) {
            console.error('Error fetching availability data:', error);
            setAvailabilityData({});
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchAvailabilityData();
    }, [currentDate]);

    // Handle date click
    const handleDateClick = (date) => {
        setSelectedDate(date);
    };

    // Handle month navigation
    const navigateMonth = (direction) => {
        setCurrentDate((prev) => prev.clone().add(direction, 'month'));
    };

    // Get dot color based on availability status
    const getDotColor = (date) => {
        const dateStr = date.format('YYYY-MM-DD');
        const status = availabilityData[dateStr];

        if (!status) return '#d9d9d9'; // Gray for no data

        switch (status) {
            case availabilityStatuses.available.value:
                return '#52c41a'; // Green
            case availabilityStatuses.limited.value:
                return '#faad14'; // Orange/Yellow
            case availabilityStatuses.offline.value:
                return '#ff4d4f'; // Red
            default:
                return '#d9d9d9'; // Gray
        }
    };

    const calendarDays = generateCalendarDays();
    const today = moment();

    return (
        <div className="availability-calendar">
            <Card>
                {/* Calendar Header */}
                <div className="gx-d-flex gx-justify-content-between gx-align-items-center gx-mb-4">
                    <Button
                        icon={<LeftOutlined />}
                        onClick={() => navigateMonth(-1)}
                        type="text"
                    />
                    <Title level={4} className="gx-mb-0">
                        {currentDate.format('MMMM YYYY')}
                    </Title>
                    <Button
                        icon={<RightOutlined />}
                        onClick={() => navigateMonth(1)}
                        type="text"
                    />
                </div>

                {/* Loading indicator */}
                {isLoading && (
                    <div className="gx-text-center gx-mb-3">
                        <Spin size="small" /> Loading availability...
                    </div>
                )}

                {/* Debug info */}
                {!isLoading && (
                    <div className="gx-text-center gx-mb-3">
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                            Loaded data for{' '}
                            {Object.keys(availabilityData).length} days
                        </Text>
                    </div>
                )}

                {/* Days of week header */}
                <div className="gx-d-flex gx-mb-2">
                    {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(
                        (day) => (
                            <div
                                key={day}
                                className="gx-flex-1 gx-text-center gx-py-2"
                                style={{ fontWeight: 'bold', fontSize: '12px' }}
                            >
                                {day}
                            </div>
                        )
                    )}
                </div>

                {/* Calendar Grid */}
                <div className="calendar-grid">
                    {Array.from(
                        { length: Math.ceil(calendarDays.length / 7) },
                        (_, weekIndex) => (
                            <div key={weekIndex} className="gx-d-flex">
                                {calendarDays
                                    .slice(weekIndex * 7, (weekIndex + 1) * 7)
                                    .map((day, dayIndex) => {
                                        const isCurrentMonth = day.isSame(
                                            currentDate,
                                            'month'
                                        );
                                        const isToday = day.isSame(
                                            today,
                                            'day'
                                        );
                                        const isSelected = day.isSame(
                                            selectedDate,
                                            'day'
                                        );
                                        const dotColor = getDotColor(day);

                                        return (
                                            <div
                                                key={dayIndex}
                                                className={`calendar-day gx-flex-1 gx-text-center gx-py-3 gx-cursor-pointer gx-position-relative ${
                                                    isCurrentMonth
                                                        ? ''
                                                        : 'gx-text-grey'
                                                } ${isToday ? 'today' : ''} ${isSelected ? 'selected' : ''}`}
                                                onClick={() =>
                                                    isCurrentMonth &&
                                                    handleDateClick(day)
                                                }
                                                style={{
                                                    border: '1px solid #f0f0f0',
                                                    backgroundColor: isSelected
                                                        ? '#1890ff'
                                                        : isToday
                                                          ? '#e6f7ff'
                                                          : 'transparent',
                                                    minHeight: '50px',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    position: 'relative',
                                                }}
                                            >
                                                <Text
                                                // style={{
                                                //     fontSize: '14px',
                                                //     fontWeight: isToday
                                                //         ? 'bold'
                                                //         : 'normal',
                                                //     color: isSelected
                                                //         ? 'white'
                                                //         : 'inherit',
                                                // }}
                                                >
                                                    {day.date()}
                                                </Text>

                                                {/* Availability dot */}
                                                {isCurrentMonth && (
                                                    <div
                                                        style={{
                                                            width: '6px',
                                                            height: '6px',
                                                            borderRadius: '50%',
                                                            backgroundColor:
                                                                dotColor,
                                                            marginTop: '2px',
                                                            opacity:
                                                                availabilityData[
                                                                    day.format(
                                                                        'YYYY-MM-DD'
                                                                    )
                                                                ]
                                                                    ? 1
                                                                    : 0.3,
                                                        }}
                                                    />
                                                )}
                                            </div>
                                        );
                                    })}
                            </div>
                        )
                    )}
                </div>

                {/* Legend */}
                <div
                    className="gx-mt-4 gx-d-flex gx-justify-content-center gx-align-items-center gx-flex-wrap"
                    style={{ gap: '16px' }}
                >
                    <div
                        className="gx-d-flex gx-align-items-center"
                        style={{ gap: '6px' }}
                    >
                        <div
                            style={{
                                width: '8px',
                                height: '8px',
                                borderRadius: '50%',
                                backgroundColor: '#52c41a',
                            }}
                        />
                        <Text style={{ fontSize: '12px' }}>Available</Text>
                    </div>
                    <div
                        className="gx-d-flex gx-align-items-center"
                        style={{ gap: '6px' }}
                    >
                        <div
                            style={{
                                width: '8px',
                                height: '8px',
                                borderRadius: '50%',
                                backgroundColor: '#faad14',
                            }}
                        />
                        <Text style={{ fontSize: '12px' }}>Limited</Text>
                    </div>
                    <div
                        className="gx-d-flex gx-align-items-center"
                        style={{ gap: '6px' }}
                    >
                        <div
                            style={{
                                width: '8px',
                                height: '8px',
                                borderRadius: '50%',
                                backgroundColor: '#ff4d4f',
                            }}
                        />
                        <Text style={{ fontSize: '12px' }}>Offline</Text>
                    </div>
                </div>
            </Card>

            {/* Selected Date Details */}
            {selectedDate && (
                <Card className="gx-mt-4" bodyStyle={{ padding: 0 }}>
                    {/* <div className="selected-date-header">
                        <Title
                            level={4}
                            className="gx-mb-1"
                            style={{ color: 'white' }}
                        >
                            {selectedDate.format('dddd, MMMM D, YYYY')}
                        </Title>
                        <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                            {selectedDate.format('dddd').toUpperCase()}{' '}
                            {selectedDate.format('DD')}
                        </Text>
                    </div> */}
                    <div className="selected-date-header">
                        <Title
                            level={4}
                            className="gx-mb-1"
                            style={{ color: 'white' }}
                        >
                            {selectedDate.format('ddd,D')}
                        </Title>
                        {/* <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                            {selectedDate.format('dddd').toUpperCase()}{' '}
                            {selectedDate.format('DD')}
                        </Text> */}
                    </div>

                    <div style={{ padding: '24px' }}>
                        <SingleDay
                            day={{
                                date: selectedDate.format('YYYY-MM-DD'),
                                dayName: selectedDate.format('dddd'),
                            }}
                            onNewStatus={(statusCode) => {
                                // Update the availability data when status changes
                                setAvailabilityData((prev) => ({
                                    ...prev,
                                    [selectedDate.format('YYYY-MM-DD')]:
                                        statusCode,
                                }));
                            }}
                            canUpdate={canUpdateDate(selectedDate)}
                        />
                    </div>
                </Card>
            )}
        </div>
    );
};

export default AvailabilityCalendar;
