import React, { Component } from 'react';
import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>u, <PERSON>, Tag } from 'antd';
import CustomScrollbars from '../../../util/CustomScrollbars';
import AppModuleHeader from '../../../components/AppModuleHeader';
import { filters } from './filters';
import PagedApiListView from '../../../components/wify-utils/crud/overview/PagedApiListView';
import Avatar from 'antd/lib/avatar/avatar';
import { SideBar } from './SideBar';
import LeaveEditor from './leaveEditor';
import {
    convertUTCToDisplayTime,
    getAnyObjectFrFilter,
    getLinktoObject,
    getRandomBgColor,
    handleFilterClearDateIfNull,
    isMobileView,
} from '../../../util/helpers';
import { getFilterFormMetaFilledWithApiData } from '../../../components/wify-utils/crud/overview/filter_helpers';
import CircularProgress from '../../../components/CircularProgress';
import http_utils from '../../../util/http_utils';
import IconWithTextCard from '../../../components/IconWithTextCard';
import { DownloadOutlined, EditFilled, EyeFilled } from '@ant-design/icons';
import ExporterModal from './ExporterModal';

const StatusVsColor = {
    Approved: 'success',
    Pending: 'processing',
    Rejected: 'warning',
};
const protoUrl = '/attendance/capacity_wise_availability';
class Availability extends Component {
    constructor(props) {
        super(props);
    }

    state = {
        activeFilters: this.getFiltersFrmSearch(),
        searchFilter: this.getSearchFromUrl(),
        drawerState: false,
        showEditor: false,
        showItemEditor: false,
        isLoadingViewData: false,
        viewData: undefined,
        error: '',
        editModeForceRefreshDone: false,
        showExporter: false,
        customPageSize: true,
    };

    componentDidMount() {
        this.initViewData();
    }

    isAccessMyLeave() {
        return this.props.match.path == '/my-leaves';
    }

    initViewData() {
        if (this.state.viewData == undefined && !this.state.isLoadingViewData) {
            this.setState({
                isLoadingViewData: true,
            });
            handleFilterClearDateIfNull(this.state.activeFilters);
            var params = {
                filters: this.state.activeFilters,
                search_query: this.props.searchQuery
                    ? this.props.searchQuery
                    : '',
            };
            const onComplete = (resp) => {
                console.log('resp data', resp.data);
                this.setState({
                    isLoadingViewData: false,
                    viewData: resp.data,
                    error: '',
                });
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };

            if (this.isAccessMyLeave()) {
                http_utils.performGetCall(
                    protoUrl + '/myleaves_overview_proto',
                    params,
                    onComplete,
                    onError
                );
            } else {
                http_utils.performGetCall(
                    protoUrl + '/overview_proto',
                    params,
                    onComplete,
                    onError
                );
            }
        }
    }

    getFiltersFrmSearch() {
        let parsedFilter = new URLSearchParams(window.location.search).get(
            'filters'
        );
        return parsedFilter ? JSON.parse(parsedFilter) : {};
    }

    getSearchFromUrl() {
        let searchFrmLink = new URLSearchParams(window.location.search).get(
            'query'
        );
        return searchFrmLink ? searchFrmLink : '';
    }

    resetFilter = () => {
        this.setState({
            activeFilters: {},
        });
    };

    handleFilterChange = (newFilterObject) => {
        this.setState({
            activeFilters: {
                ...newFilterObject,
            },
        });
    };

    handleSearchChange = (query) => {
        this.setState({
            searchFilter: query,
        });
    };

    onToggleDrawer() {
        this.setState({
            drawerState: !this.state.drawerState,
        });
    }

    getFilters() {
        var staticFilters = [...filters];
        if (this.isAccessMyLeave()) {
            staticFilters = staticFilters.filter(
                (item) =>
                    item.key != 'locations' &&
                    item.key != 'fully_present' &&
                    item.key != 'technician_roles'
            );
        }
        var defaultAnyMeta = getAnyObjectFrFilter();
        var filtersFrmViewData = this.state.viewData?.filters_proto;
        var finalFilter = getFilterFormMetaFilledWithApiData(
            staticFilters,
            defaultAnyMeta,
            filtersFrmViewData
        );
        return finalFilter;
    }

    onAddLeave = () => {
        this.setState({ showEditor: true });
    };

    onSingleRowClick = (item) => {
        this.setState({
            editorItem: item,
            showItemEditor: true,
        });
    };

    configFrPagedApiListView = {
        dataSourceApi: !this.isAccessMyLeave()
            ? '/attendance/availability'
            : '/attendance/my_leaves',
        columns: [
            {
                key: 'technician_name',
                dataIndex: 'technician_name',
                title: 'Technician name',
            },
            {
                key: 'leave_type',
                dataIndex: 'leave_type',
                title: 'Leave type',
            },
            {
                key: 'start_and_end_date',
                dataIndex: 'start_and_end_date',
                title: 'Start date - End date',
                render: (text, record) => {
                    if (record.start_date != record.end_date) {
                        return (
                            <span>
                                {convertUTCToDisplayTime(
                                    record.start_date,
                                    true
                                )}{' '}
                                -{' '}
                                {convertUTCToDisplayTime(record.end_date, true)}
                            </span>
                        );
                    } else {
                        return (
                            <span>
                                {convertUTCToDisplayTime(
                                    record.start_date,
                                    true
                                )}
                            </span>
                        );
                    }
                },
            },
            {
                key: 'remarks',
                dataIndex: 'remarks',
                title: 'Remarks',
            },
            {
                key: 'added_by',
                dataIndex: 'added_by',
                title: 'Added by',
            },
            {
                key: 'approved_by',
                dataIndex: 'approved_by',
                title: 'Responsible',
            },
            {
                key: 'time',
                dataIndex: 'time',
                title: 'Time',
                render: (text, record) => {
                    if (record.time != '') {
                        return convertUTCToDisplayTime(record.time);
                    }
                },
            },
            {
                key: 'status',
                dataIndex: 'status',
                title: 'Status',
                render: (text, record) => {
                    return <Tag color={StatusVsColor[text]}> {text} </Tag>;
                },
            },
            !this.isAccessMyLeave()
                ? {
                      key: 'action',
                      title: 'Action',
                      render: (text, record) => {
                          if (record.leave_type != 'Present') {
                              return (
                                  <EditFilled
                                      onClick={() =>
                                          this.onSingleRowClick(record)
                                      }
                                  />
                              );
                          }
                      },
                  }
                : {},
        ],
        renderSingleItem: (item) => (
            <div
                className="gx-d-flex border_bottom gx-w-100 gx-p-2"
                onClick={() => this.onSingleRowClick(item)}
            >
                <div className="gx-module-list-icon">
                    <div className="gx-ml-2 gx-d-none gx-d-sm-flex">
                        <Avatar size="large">
                            {item.technician_name.charAt(0).toUpperCase()}
                        </Avatar>
                    </div>
                </div>

                <div className="gx-module-list-info gx-contact-list-info">
                    <div className="gx-module-contact-content">
                        <p className="gx-mb-1">
                            <span className="gx-text-truncate gx-contact-name">
                                {item.technician_name}
                            </span>
                        </p>

                        <div className="gx-text-muted">
                            <div className="gx-mb-0" style={{ marginTop: 10 }}>
                                <div
                                    className={`gx-badge gx-text-white gx-mb-10 ${getRandomBgColor()}`}
                                    key={item.status}
                                >
                                    {item.status}
                                </div>
                                {item.approved_by != null ? (
                                    <>
                                        {' '}
                                        {item.approved_by}{' '}
                                        <small>
                                            {convertUTCToDisplayTime(
                                                item.u_time
                                            )}
                                        </small>{' '}
                                    </>
                                ) : (
                                    ''
                                )}
                            </div>
                            {item.remarks}
                        </div>
                    </div>
                </div>
            </div>
        ),
    };

    notifyDataSetChanged(entry_id) {
        console.log('Refresh list called in parent', this);
        if (this.state.editorItem == undefined) {
            console.log('Editor item was undefined, refreshing list');
            // new entry was created, we will have to clear filters
            // refresh list
            this.resetFilter();
        } else {
            // Refresh along with existing filters
            this.setState({
                activeFilters: { ...this.state.activeFilters },
            });
        }
    }

    getExportMenuData() {
        return (
            <Menu>
                <Menu.Item
                    key="export_request"
                    onClick={(e) => this.onExportClick()}
                >
                    <span>
                        <DownloadOutlined /> Export request
                    </span>
                </Menu.Item>
            </Menu>
        );
    }

    onExportClick() {
        // message.success('Initiating export');
        this.setState({ showExporter: true });
    }

    getDashboardData() {
        let dashbaord_data = this.state.viewData?.dashbaord_data?.[0];
        let userDashboard = [];
        if (!this.isAccessMyLeave()) {
            userDashboard.push(
                {
                    count: dashbaord_data ? dashbaord_data?.total : 0,
                    title: 'Total users',
                    color: '#ffc107',
                    key: 'total_user',
                },
                {
                    count: dashbaord_data ? dashbaord_data?.present : 0,
                    title: 'Fully Present',
                    color: '#4caf50',
                    key: 'fully_present',
                    value: true,
                }
            );
        }

        let dashbaord_count_data = [
            ...userDashboard,
            {
                count: dashbaord_data ? dashbaord_data?.leave_applications : 0,
                title: 'Leave Applications',
                color: '#607d8b',
                key: 'approval',
                value: '-1',
            },
            {
                count: dashbaord_data ? dashbaord_data?.approval_pending : 0,
                title: 'Pending Approval',
                key: 'approval',
                value: 'Pending',
                color: '#e57373',
            },
        ];
        return dashbaord_count_data;
    }

    render() {
        const {
            activeFilters,
            drawerState,
            showEditor,
            editorItem,
            searchFilter,
            showItemEditor,
            isLoadingViewData,
            error,
            viewData,
            showExporter,
        } = this.state;

        let dashbaord_count = this.getDashboardData();

        return (
            <>
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <div className="gx-main-content">
                        <div className="gx-app-module">
                            <div className="gx-d-block gx-d-lg-none">
                                <Drawer
                                    placement="left"
                                    closable={false}
                                    visible={drawerState}
                                    onClose={this.onToggleDrawer.bind(this)}
                                >
                                    <SideBar
                                        filters={this.getFilters()}
                                        onFilterChange={this.handleFilterChange}
                                        activeFilters={activeFilters}
                                        onAddClick={this.onAddLeave}
                                        isAccessMyLeave={this.isAccessMyLeave()}
                                    />
                                </Drawer>
                            </div>
                            <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                                <SideBar
                                    filters={this.getFilters()}
                                    onFilterChange={this.handleFilterChange}
                                    activeFilters={activeFilters}
                                    onAddClick={this.onAddLeave}
                                    isAccessMyLeave={this.isAccessMyLeave()}
                                />
                            </div>

                            {
                                <ExporterModal
                                    showEditor={showExporter}
                                    filters={activeFilters}
                                    onClose={() => {
                                        this.setState({ showExporter: false });
                                    }}
                                />
                            }
                            {/* Creator popup */}
                            <LeaveEditor
                                showEditor={showEditor}
                                onClose={() => {
                                    this.setState({ showEditor: false });
                                }}
                                onDataModified={(entry_id) => {
                                    this.notifyDataSetChanged(entry_id);
                                }}
                                isAccessMyLeave={this.isAccessMyLeave()}
                            />

                            <div className="gx-module-box">
                                <div className="gx-module-box-header">
                                    <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                        <i
                                            className="icon icon-filter gx-icon-btn"
                                            aria-label="Menu"
                                            onClick={this.onToggleDrawer.bind(
                                                this
                                            )}
                                        />
                                    </span>

                                    <AppModuleHeader
                                        placeholder="Search by Technician name, Leave type.."
                                        currValue={this.state.searchFilter}
                                        onChange={this.handleSearchChange}
                                        optionsMenuData={this.getExportMenuData()}
                                    />
                                </div>

                                <Row className="gx-p-2 gx-bg-white gx-border">
                                    {isMobileView() && (
                                        <>
                                            <Col xs={24}>
                                                <Button
                                                    className="gx-w-100 ant-btn wy-add-leave-btn"
                                                    type="primary"
                                                    aria-label="add"
                                                    onClick={this.onAddLeave}
                                                >
                                                    <i className="icon icon-signup gx-mr-2" />
                                                    <span>Add Leave</span>
                                                </Button>
                                            </Col>
                                        </>
                                    )}

                                    {dashbaord_count?.map(
                                        (singleDashbaordCount, index) => (
                                            <Col
                                                xs={12}
                                                md={5}
                                                lg={6}
                                                key={index}
                                            >
                                                {
                                                    <IconWithTextCard
                                                        className="gx-mb-0"
                                                        cardColorCode={
                                                            singleDashbaordCount.color
                                                        }
                                                        title={
                                                            singleDashbaordCount.count
                                                        }
                                                        subTitle={
                                                            singleDashbaordCount.title
                                                        }
                                                        cardSpace={true}
                                                    />
                                                }
                                            </Col>
                                        )
                                    )}
                                </Row>

                                <div className="gx-module-box-content gx-px-4 gx-py-3">
                                    <CustomScrollbars>
                                        <PagedApiListView
                                            {...this.configFrPagedApiListView}
                                            // columns={3}
                                            filterObject={activeFilters}
                                            searchQuery={searchFilter}
                                            customPageSize={
                                                this.state.customPageSize
                                            }
                                            tableView
                                        />

                                        {/* Edit popup */}
                                        <LeaveEditor
                                            showEditor={showItemEditor}
                                            editorItem={editorItem}
                                            editMode={true}
                                            onClose={() => {
                                                this.setState({
                                                    showItemEditor: false,
                                                });
                                            }}
                                            onDataModified={(entry_id) => {
                                                this.notifyDataSetChanged(
                                                    entry_id
                                                );
                                            }}
                                            isAccessMyLeave={this.isAccessMyLeave()}
                                        />
                                    </CustomScrollbars>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </>
        );
    }
}

export default Availability;
