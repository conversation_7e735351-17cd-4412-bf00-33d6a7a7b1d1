import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { <PERSON><PERSON>, Col, Drawer, <PERSON>u, Row, Tag } from 'antd';
import { useHistory, useLocation } from 'react-router-dom';
import CustomScrollbars from '../../../util/CustomScrollbars';
import AppModuleHeader from '../../../components/AppModuleHeader';
import { filters } from './filters';
import PagedApiListView from '../../../components/wify-utils/crud/overview/PagedApiListView';
import Avatar from 'antd/lib/avatar/avatar';
import { SideBar } from './SideBar';

import {
    convertUTCToDisplayTime,
    getAnyObjectFrFilter,
    getLinktoObject,
    getRandomBgColor,
    handleFilterClearDateIfNull,
    isMobileView,
} from '../../../util/helpers';
import { getFilterFormMetaFilledWithApiData } from '../../../components/wify-utils/crud/overview/filter_helpers';
import CircularProgress from '../../../components/CircularProgress';
import http_utils from '../../../util/http_utils';
import IconWithTextCard from '../../../components/IconWithTextCard';
import { DownloadOutlined, EditFilled, EyeFilled } from '@ant-design/icons';
import ExporterModal from './ExporterModal';

const StatusVsColor = {
    Available: 'success',
    Limited: 'processing',
    Offline: 'error',
};

const protoUrl = '/attendance/capacity_wise_availability';

const CapacityWiseAvailability = () => {
    const history = useHistory();
    const location = useLocation();

    // Helper functions to get initial state
    const getFiltersFrmSearch = useCallback(() => {
        let parsedFilter = new URLSearchParams(location.search).get('filters');
        return parsedFilter ? JSON.parse(parsedFilter) : {};
    }, [location.search]);

    const getSearchFromUrl = useCallback(() => {
        let searchFrmLink = new URLSearchParams(location.search).get('query');
        return searchFrmLink ? searchFrmLink : '';
    }, [location.search]);

    // State management
    const [activeFilters, setActiveFilters] = useState(getFiltersFrmSearch());
    const [searchFilter, setSearchFilter] = useState(getSearchFromUrl());
    const [drawerState, setDrawerState] = useState(false);
    const [showEditor, setShowEditor] = useState(false);
    const [showItemEditor, setShowItemEditor] = useState(false);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [viewData, setViewData] = useState(undefined);
    const [error, setError] = useState('');
    const [showExporter, setShowExporter] = useState(false);
    const [editorItem, setEditorItem] = useState(undefined);
    const [customPageSize] = useState(true);

    // Helper function to check if this is my leaves access
    const isAccessMyLeave = useCallback(() => {
        return location.pathname === '/my-leaves';
    }, [location.pathname]);

    // Initialize view data
    const initViewData = useCallback(() => {
        if (viewData === undefined && !isLoadingViewData) {
            setIsLoadingViewData(true);
            handleFilterClearDateIfNull(activeFilters);

            const params = {
                filters: activeFilters,
                search_query: '',
            };

            const onComplete = (resp) => {
                console.log('resp data', resp.data);
                setIsLoadingViewData(false);
                setViewData(resp.data);
                setError('');
            };

            const onError = (error) => {
                setIsLoadingViewData(false);
                setError(http_utils.decodeErrorToMessage(error));
            };

            if (isAccessMyLeave()) {
                http_utils.performGetCall(
                    protoUrl + '/myleaves_overview_proto',
                    params,
                    onComplete,
                    onError
                );
            } else {
                http_utils.performGetCall(
                    protoUrl + '/overview_proto',
                    params,
                    onComplete,
                    onError
                );
            }
        }
    }, [viewData, isLoadingViewData, activeFilters, isAccessMyLeave]);

    // Effect to initialize data on mount
    useEffect(() => {
        initViewData();
    }, [initViewData]);

    // Handler functions
    const resetFilter = useCallback(() => {
        setActiveFilters({});
    }, []);

    const handleFilterChange = useCallback((newFilterObject) => {
        setActiveFilters({ ...newFilterObject });
    }, []);

    const handleSearchChange = useCallback((query) => {
        setSearchFilter(query);
    }, []);

    const onToggleDrawer = useCallback(() => {
        setDrawerState(!drawerState);
    }, [drawerState]);

    // Get filters for the component
    const getFilters = useCallback(() => {
        let staticFilters = [...filters];
        if (isAccessMyLeave()) {
            staticFilters = staticFilters.filter(
                (item) =>
                    item.key !== 'locations' &&
                    item.key !== 'fully_present' &&
                    item.key !== 'technician_roles'
            );
        }
        const defaultAnyMeta = getAnyObjectFrFilter();
        const filtersFrmViewData = viewData?.filters_proto;
        const finalFilter = getFilterFormMetaFilledWithApiData(
            staticFilters,
            defaultAnyMeta,
            filtersFrmViewData
        );
        return finalFilter;
    }, [isAccessMyLeave, viewData]);

    const onSingleRowClick = useCallback((item) => {
        setEditorItem(item);
        setShowItemEditor(true);
    }, []);

    // Configuration for PagedApiListView - updated for capacity-wise availability
    const configFrPagedApiListView = useMemo(
        () => ({
            dataSourceApi: '/attendance/capacity_wise_availability',
            columns: [
                {
                    key: 'technician_name',
                    dataIndex: 'technician_name',
                    title: 'Technician name',
                },
                {
                    key: 'is_available',
                    dataIndex: 'is_available',
                    title: 'Is available',
                },
                {
                    key: 'day',
                    dataIndex: 'day',
                    title: 'Day',
                },
                {
                    key: 'time_slots',
                    dataIndex: 'time_slots',
                    title: 'Time Slot',
                },

                {
                    key: 'updated_on',
                    dataIndex: 'updated_on',
                    title: 'Updated on',
                    render: (text, record) => {
                        if (record.updated_on) {
                            return convertUTCToDisplayTime(record.updated_on);
                        }
                        return '-';
                    },
                },
            ],
            renderSingleItem: (item) => (
                <div
                    className="gx-d-flex border_bottom gx-w-100 gx-p-2"
                    onClick={() => onSingleRowClick(item)}
                >
                    <div className="gx-module-list-icon">
                        <div className="gx-ml-2 gx-d-none gx-d-sm-flex">
                            <Avatar size="large">
                                {item.full_name.charAt(0).toUpperCase()}
                            </Avatar>
                        </div>
                    </div>

                    <div className="gx-module-list-info gx-contact-list-info">
                        <div className="gx-module-contact-content">
                            <p className="gx-mb-1">
                                <span className="gx-text-truncate gx-contact-name">
                                    {item.full_name}
                                </span>
                            </p>

                            <div className="gx-text-muted">
                                <div
                                    className="gx-mb-0"
                                    style={{ marginTop: 10 }}
                                >
                                    <div
                                        className={`gx-badge gx-text-white gx-mb-10 ${getRandomBgColor()}`}
                                        key={item.availability_status}
                                    >
                                        {item.availability_status}
                                    </div>
                                    <small>
                                        {item.available_slots}/
                                        {item.total_slots} slots available
                                    </small>
                                </div>
                                {item.email}
                            </div>
                        </div>
                    </div>
                </div>
            ),
        }),
        [onSingleRowClick]
    );

    // Data change notification handler
    const notifyDataSetChanged = useCallback(
        (entry_id) => {
            console.log('Refresh list called in parent');
            if (editorItem === undefined) {
                console.log('Editor item was undefined, refreshing list');
                // new entry was created, we will have to clear filters
                resetFilter();
            } else {
                // Refresh along with existing filters
                setActiveFilters({ ...activeFilters });
            }
        },
        [editorItem, resetFilter, activeFilters]
    );

    // Export menu data
    const getExportMenuData = useCallback(() => {
        return (
            <Menu>
                <Menu.Item
                    key="export_request"
                    onClick={() => setShowExporter(true)}
                >
                    <span>
                        <DownloadOutlined /> Export report
                    </span>
                </Menu.Item>
                <Menu.Item
                    key="export_raw_data"
                    onClick={() => setShowExporter(true)}
                >
                    <span>
                        <DownloadOutlined /> Export raw data
                    </span>
                </Menu.Item>
            </Menu>
        );
    }, []);

    // Dashboard data for capacity-wise availability
    const getDashboardData = useCallback(() => {
        const dashboardData = viewData?.data;

        const dashboardCountData = [
            {
                count: dashboardData ? dashboardData?.total_capacity : 0,
                title: 'Total Users',
                color: '#ffc107',
                key: 'total_capacity',
            },
            {
                count: dashboardData ? dashboardData?.available_capacity : 0,
                title: 'Fully Present',
                color: '#4caf50',
                key: 'available_capacity',
            },
            {
                count: dashboardData ? dashboardData?.utilized_capacity : 0,
                title: 'Limited',
                color: '#607d8b',
                key: 'utilized_capacity',
            },
            {
                count: dashboardData
                    ? `${dashboardData?.utilization_percentage}`
                    : 0,
                title: 'Offline',
                color: '#e57373',
                key: 'utilization_percentage',
            },
        ];
        return dashboardCountData;
    }, [viewData]);

    // Get dashboard count data
    const dashboardCount = getDashboardData();

    // Render the component
    return (
        <>
            {isLoadingViewData ? (
                <div className="gx-loader-view gx-loader-position">
                    <CircularProgress />
                </div>
            ) : viewData === undefined ? (
                <p className="gx-text-red">{error}</p>
            ) : (
                <div className="gx-main-content">
                    <div className="gx-app-module">
                        <div className="gx-d-block gx-d-lg-none">
                            <Drawer
                                placement="left"
                                closable={false}
                                visible={drawerState}
                                onClose={onToggleDrawer}
                            >
                                <SideBar
                                    filters={getFilters()}
                                    onFilterChange={handleFilterChange}
                                    activeFilters={activeFilters}
                                    //onAddClick={onAddLeave}
                                    isAccessMyLeave={isAccessMyLeave()}
                                />
                            </Drawer>
                        </div>
                        <div className="gx-module-sidenav gx-d-none gx-d-lg-flex">
                            <SideBar
                                filters={getFilters()}
                                onFilterChange={handleFilterChange}
                                activeFilters={activeFilters}
                                //onAddClick={onAddLeave}
                                isAccessMyLeave={isAccessMyLeave()}
                            />
                        </div>

                        <ExporterModal
                            showEditor={showExporter}
                            filters={activeFilters}
                            onClose={() => setShowExporter(false)}
                        />

                        {/* Creator popup */}

                        <div className="gx-module-box">
                            <div className="gx-module-box-header">
                                <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                    <i
                                        className="icon icon-filter gx-icon-btn"
                                        aria-label="Menu"
                                        onClick={onToggleDrawer}
                                    />
                                </span>

                                <AppModuleHeader
                                    placeholder="Search by User name, Email.."
                                    currValue={searchFilter}
                                    onChange={handleSearchChange}
                                    optionsMenuData={getExportMenuData()}
                                />
                            </div>

                            <Row className="gx-p-2 gx-bg-white gx-border">
                                {dashboardCount?.map(
                                    (singleDashboardCount, index) => (
                                        <Col xs={12} md={6} lg={6} key={index}>
                                            <IconWithTextCard
                                                className="gx-mb-0"
                                                cardColorCode={
                                                    singleDashboardCount.color
                                                }
                                                title={
                                                    singleDashboardCount.count
                                                }
                                                subTitle={
                                                    singleDashboardCount.title
                                                }
                                                cardSpace={true}
                                            />
                                        </Col>
                                    )
                                )}
                            </Row>

                            <div className="gx-module-box-content gx-px-4 gx-py-3">
                                <CustomScrollbars>
                                    <PagedApiListView
                                        {...configFrPagedApiListView}
                                        filterObject={activeFilters}
                                        searchQuery={searchFilter}
                                        customPageSize={customPageSize}
                                        tableView
                                    />
                                </CustomScrollbars>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default CapacityWiseAvailability;
