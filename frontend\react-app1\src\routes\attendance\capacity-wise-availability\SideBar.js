import React, { Component } from 'react';
import { Button } from 'antd';
import CustomScrollbars from '../../../util/CustomScrollbars';
import QuickFilters from '../../../components/wify-utils/crud/overview/QuickFilters';
import ConfigHelpers from '../../../util/ConfigHelpers';
import { isMobileView } from '../../../util/helpers';

export class SideBar extends Component {
    state = {
        activeFilters: this.props.activeFilters,
    };

    handleFilterChange = (newFilterObject) => {
        this.setState(
            {
                activeFilters: {
                    ...newFilterObject,
                },
            },
            () => this.props.onFilterChange(this.state.activeFilters)
        );
    };

    componentDidUpdate(prevProps, prevState) {
        if (prevProps.activeFilters != this.props.activeFilters) {
            this.setState({
                activeFilters: this.props.activeFilters,
            });
        }
    }

    render() {
        const { activeFilters } = this.state;
        const filters = this.props.filters;
        return (
            <div className="gx-module-side">
                <div className="gx-module-side-header">
                    <div className="gx-module-logo">
                        <i className="icon icon-contacts gx-mr-4" />
                        <span>
                            {this.props.isAccessMyLeave
                                ? 'My leaves'
                                : 'Leaves M'}
                        </span>
                    </div>
                </div>

                <div className="gx-module-side-content">
                    <CustomScrollbars className="gx-module-side-scroll">
                        {!isMobileView() && (
                            <div className="gx-module-add-task">
                                <Button
                                    className="gx-btn-block ant-btn"
                                    type="primary"
                                    aria-label="add"
                                    onClick={this.props.onAddClick}
                                >
                                    <i className="icon icon-signup gx-mr-2" />
                                    <span>Add Leave</span>
                                </Button>
                            </div>
                            
                        )}
                        <QuickFilters
                            filters={filters}
                            onFilterChange={this.handleFilterChange}
                            activeFilters={activeFilters}
                            linkMode
                        />
                    </CustomScrollbars>
                </div>
            </div>
        );
    }
}
