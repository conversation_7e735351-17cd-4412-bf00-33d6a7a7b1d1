import React from 'react';
import { act, render, screen } from '@testing-library/react';
import LineItems from './LineItems';
import ConfigHelpers from '../../util/ConfigHelpers';
import http_utils from '../../util/http_utils';
import userEvent from '@testing-library/user-event';
const prefix = 'LineItems';

//mock ConfigHelpers
jest.mock('../../util/ConfigHelpers');

jest.mock('react-countup', () => ({
    __esModule: true,
    default: ({ end }) => <span>{end}</span>,
}));

beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});

const props = {
    config: {
        'a3ee4e46-3882-4306-8660-4c97d1bc4601': {
            key: 'a3ee4e46-3882-4306-8660-4c97d1bc4601',
            label: 'Installation',
            fields: '{"originalFields":[{"id":"923c761f-001b-43b5-ba66-1c07a6a7165e","element":"Dropdown","label":{"blocks":[{"key":"408n8","text":"Area","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"b5fdf909-aaab-421c-a4de-b93f727d4657","value":"Chair"},{"id":"83298b38-a7c2-47a3-b3b3-4bf8e52c0993","value":"Table"},{"id":"d3974583-55fc-41a2-ba28-c24ab1fec80a","value":"Sofa"}]},{"id":"a9de49bc-95e4-445b-8b28-8db6e748f865","element":"NumberInput","required":false,"label":{"blocks":[{"key":"8fp6r","text":"height","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0},{"id":"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b","element":"NumberInput","required":false,"label":{"blocks":[{"key":"6eg50","text":"width","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"value":0}],"translatedFields":[{"key":"923c761f-001b-43b5-ba66-1c07a6a7165e","required":false,"label":"Area","widget":"select","options":[{"label":"Chair","value":"b5fdf909-aaab-421c-a4de-b93f727d4657"},{"label":"Table","value":"83298b38-a7c2-47a3-b3b3-4bf8e52c0993"},{"label":"Sofa","value":"d3974583-55fc-41a2-ba28-c24ab1fec80a"}]},{"key":"a9de49bc-95e4-445b-8b28-8db6e748f865","required":false,"label":"height","widget":"number"},{"key":"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b","required":false,"label":"width","widget":"number"}]}',
            name_field_label: 'Area',
            name_field_formula: '{Area} - {height}',
            quantity_field_label: 'quantity',
            quantity_field_formula: '{width} + {height}',
            price_field_label: 'Price per Sqft',
        },
    },
    srvcConfigData: {
        qty: '',
        rate: '',
        total: '',
        org_id: 2,
        usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
        entry_id: 3,
        ip_address: '::1',
        user_agent:
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
        authority_id: [2, 140, 145],
        sbtsk_fr_145: 4,
        sbtsk_fr_146: 13,
        sbtsk_fr_147: 10,
        srvc_type_id: [57, 16],
        vertical_desc: 'TEst',
        sp_authority_2: 140,
        vertical_title: 'LF',
        rating_type_145: 'another_authority',
        vertical_nature: 'project_based',
        enable_sp_rating: true,
        sp_authority_140: 2,
        sp_authority_145: 2,
        sp_rating_type_2: 'another_authority',
        sp_rating_type_140: 'static_user',
        sp_rating_type_145: 'another_authority',
        sp_rating_type_146: 'static_user',
        sp_static_user_140: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_cust_fields_json:
            '{"originalFields":[{"id":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","element":"Dropdown","label":{"blocks":[{"key":"f3c38","text":"Placeholder Label","type":"unstyled","depth":0,"inlineStyleRanges":[],"entityRanges":[],"data":{}}],"entityMap":{}},"required":false,"options":[{"id":"0c4a9bfe-aad6-4da4-87ce-83aebab18266","value":"Option1"},{"id":"4edaef88-1f2c-4ab9-8635-16b2a79eff38","value":"Option2"}]}],"translatedFields":[{"key":"0bb78cae-b044-41c1-a3a8-9e66f17af1c6","required":false,"label":"Placeholder Label","widget":"select","options":[{"label":"Option1","value":"0c4a9bfe-aad6-4da4-87ce-83aebab18266"},{"label":"Option2","value":"4edaef88-1f2c-4ab9-8635-16b2a79eff38"}]}]}',
        sp_rating_template_2: 3,
        sp_rating_template_140: 3,
        sp_rating_template_145: 7,
        srvc_type_enable_billing: true,
        deployment_possible_roles: [145, 146, 147],
        sp_deployment_who_can_edit: [],
        srvc_type_line_item_config:
            '{"a3ee4e46-3882-4306-8660-4c97d1bc4601":{"key":"a3ee4e46-3882-4306-8660-4c97d1bc4601","label":"Installation","fields":"{\\"originalFields\\":[{\\"id\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"element\\":\\"Dropdown\\",\\"label\\":{\\"blocks\\":[{\\"key\\":\\"408n8\\",\\"text\\":\\"Area\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"required\\":false,\\"options\\":[{\\"id\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\",\\"value\\":\\"Chair\\"},{\\"id\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\",\\"value\\":\\"Table\\"},{\\"id\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\",\\"value\\":\\"Sofa\\"}]},{\\"id\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"8fp6r\\",\\"text\\":\\"height\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0},{\\"id\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"element\\":\\"NumberInput\\",\\"required\\":false,\\"label\\":{\\"blocks\\":[{\\"key\\":\\"6eg50\\",\\"text\\":\\"width\\",\\"type\\":\\"unstyled\\",\\"depth\\":0,\\"inlineStyleRanges\\":[],\\"entityRanges\\":[],\\"data\\":{}}],\\"entityMap\\":{}},\\"value\\":0}],\\"translatedFields\\":[{\\"key\\":\\"923c761f-001b-43b5-ba66-1c07a6a7165e\\",\\"required\\":false,\\"label\\":\\"Area\\",\\"widget\\":\\"select\\",\\"options\\":[{\\"label\\":\\"Chair\\",\\"value\\":\\"b5fdf909-aaab-421c-a4de-b93f727d4657\\"},{\\"label\\":\\"Table\\",\\"value\\":\\"83298b38-a7c2-47a3-b3b3-4bf8e52c0993\\"},{\\"label\\":\\"Sofa\\",\\"value\\":\\"d3974583-55fc-41a2-ba28-c24ab1fec80a\\"}]},{\\"key\\":\\"a9de49bc-95e4-445b-8b28-8db6e748f865\\",\\"required\\":false,\\"label\\":\\"height\\",\\"widget\\":\\"number\\"},{\\"key\\":\\"75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b\\",\\"required\\":false,\\"label\\":\\"width\\",\\"widget\\":\\"number\\"}]}","name_field_label":"Area","name_field_formula":"{Area} - {height}","quantity_field_label":"quantity","quantity_field_formula":"{width} + {height}","price_field_label":"Price per Sqft"}}',
        sp_authority_fr_deployment_145: '',
        sp_authority_fr_deployment_146: 2,
        sp_authority_fr_deployment_147: 2,
        srvc_type_tabular_view_columns: [
            'srvc_prvdr',
            'request_priority',
            'request_description',
            'cust_full_name',
            'creation_date',
            'request_req_date',
            'full_address',
            'sbtsks',
            'attachments',
        ],
        deployment_time_slot_lower_limit: '9:00AM',
        deployment_time_slot_upper_limit: '7:00PM',
        sp_rating_type_fr_deployment_145: 'static_user',
        sp_rating_type_fr_deployment_146: 'static_user',
        sp_rating_type_fr_deployment_147: 'authority',
        sp_static_user_fr_deployment_145: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_146: {
            key: '177886be-ba77-410a-82b2-deb762b8c1c4',
            label: 'Gaurav Pangam wify(Service provider admin)',
            value: '177886be-ba77-410a-82b2-deb762b8c1c4',
        },
        sp_static_user_fr_deployment_147: {
            key: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
            label: 'User 2',
            value: '961d2cf4-403e-4d6b-b23c-32570c4d1c16',
        },
        sp_rating_template_fr_deployment145: 7,
        sp_rating_template_fr_deployment146: 7,
        sp_rating_template_fr_deployment147: 3,
        '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': '',
        '923c761f-001b-43b5-ba66-1c07a6a7165e':
            'b5fdf909-aaab-421c-a4de-b93f727d4657',
        'a9de49bc-95e4-445b-8b28-8db6e748f865': '',
        '2_enable_cross_visibility_of_authorities': true,
        '140_enable_cross_visibility_of_authorities': true,
        '145_enable_cross_visibility_of_authorities': true,
        srvc_type_manday_pricing_config_determination_engine: 'highest',
        srvc_type_pricing_config_for_line_item: '{}',
        srvc_type_pricing_config_for_manday: '{}',
        show_line_items_to_sp: true,
    },
    lineItemsData: {
        total: 44,
        form_data: {
            'a3ee4e46-3882-4306-8660-4c97d1bc4601': [
                {
                    key: '14557874-df0f-433e-a473-550e8358b9b7',
                    qty: '8.00',
                    rate: 4,
                    total: 32,
                    input_table_id: '14557874-df0f-433e-a473-550e8358b9b7',
                    '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': 4,
                    '923c761f-001b-43b5-ba66-1c07a6a7165e':
                        'b5fdf909-aaab-421c-a4de-b93f727d4657',
                    'a9de49bc-95e4-445b-8b28-8db6e748f865': 4,
                },
                {
                    key: 'e5aa95eb-0cdb-473d-abbc-ada30185368f',
                    qty: '6.00',
                    rate: 2,
                    total: 12,
                    input_table_id: 'e5aa95eb-0cdb-473d-abbc-ada30185368f',
                    '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': 2,
                    '923c761f-001b-43b5-ba66-1c07a6a7165e':
                        '83298b38-a7c2-47a3-b3b3-4bf8e52c0993',
                    'a9de49bc-95e4-445b-8b28-8db6e748f865': 4,
                },
            ],
        },
        revisions: [],
        total_qty: 14,
        'a3ee4e46-3882-4306-8660-4c97d1bc4601_total_qty': '08.006.00',
    },
    readOnly: false,
    isSrvcReqLock: false,
    srvc_type_id: 16,
    revisionsData: [
        {
            c_by: '177886be-ba77-410a-82b2-deb762b8c1c4',
            total: 44,
            c_name: 'Gaurav',
            c_time: '2024-11-06T06:10:59.625Z',
            org_id: '2',
            usr_id: '177886be-ba77-410a-82b2-deb762b8c1c4',
            form_data: {
                'a3ee4e46-3882-4306-8660-4c97d1bc4601': [
                    {
                        key: '14557874-df0f-433e-a473-550e8358b9b7',
                        qty: '8.00',
                        rate: 4,
                        total: 32,
                        input_table_id: '14557874-df0f-433e-a473-550e8358b9b7',
                        '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': 4,
                        '923c761f-001b-43b5-ba66-1c07a6a7165e':
                            'b5fdf909-aaab-421c-a4de-b93f727d4657',
                        'a9de49bc-95e4-445b-8b28-8db6e748f865': 4,
                    },
                    {
                        key: 'e5aa95eb-0cdb-473d-abbc-ada30185368f',
                        qty: '6.00',
                        rate: 2,
                        total: 12,
                        input_table_id: 'e5aa95eb-0cdb-473d-abbc-ada30185368f',
                        '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': 2,
                        '923c761f-001b-43b5-ba66-1c07a6a7165e':
                            '83298b38-a7c2-47a3-b3b3-4bf8e52c0993',
                        'a9de49bc-95e4-445b-8b28-8db6e748f865': 4,
                    },
                ],
            },
            revisions: [
                {
                    c_by: '73b1ba23-507b-4c7c-8465-b852d2a657e3',
                    total: 0,
                    c_name: 'Shambhu',
                    c_time: '2024-11-06T06:08:18.369Z',
                    form_data: {},
                    revisions: [],
                    total_qty: 0,
                },
            ],
            total_qty: 14,
            ip_address: '::1',
            user_agent:
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36',
            'a3ee4e46-3882-4306-8660-4c97d1bc4601_total_qty': '08.006.00',
        },
    ],
    title: 'HMLL241016521022',
    heading: 'Service provider line items',
    downloadLineItemData: {
        total: 44,
        form_data: {
            'a3ee4e46-3882-4306-8660-4c97d1bc4601': [
                {
                    key: '14557874-df0f-433e-a473-550e8358b9b7',
                    qty: '8.00',
                    rate: 4,
                    total: 32,
                    input_table_id: '14557874-df0f-433e-a473-550e8358b9b7',
                    '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': 4,
                    '923c761f-001b-43b5-ba66-1c07a6a7165e':
                        'b5fdf909-aaab-421c-a4de-b93f727d4657',
                    'a9de49bc-95e4-445b-8b28-8db6e748f865': 4,
                },
                {
                    key: 'e5aa95eb-0cdb-473d-abbc-ada30185368f',
                    qty: '6.00',
                    rate: 2,
                    total: 12,
                    input_table_id: 'e5aa95eb-0cdb-473d-abbc-ada30185368f',
                    '75c0d2f7-aa9c-4cfd-afed-dd4318dcc00b': 2,
                    '923c761f-001b-43b5-ba66-1c07a6a7165e':
                        '83298b38-a7c2-47a3-b3b3-4bf8e52c0993',
                    'a9de49bc-95e4-445b-8b28-8db6e748f865': 4,
                },
            ],
        },
        revisions: [],
        total_qty: 14,
        'a3ee4e46-3882-4306-8660-4c97d1bc4601_total_qty': '08.006.00',
    },
    allow_sp_line_items_download: true,
    isCustAcces: true,
};

describe('Smoke Test for LineItems', () => {
    it(`${prefix} renders without crashing`, () => {
        render(<LineItems config={props.config} />);
    });

    //write test to check if  brand line items heding is visible
    it(`${prefix} renders brand line items heading`, () => {
        render(
            <LineItems
                config={props.config}
                srvcConfigData={props.srvcConfigData}
                lineItemsData={props.lineItemsData}
                isBrand
                readOnly={props.readOnly}
                isSrvcReqLock={props.isSrvcReqLock}
                revisionsData={props.revisionsData}
                title={props.title}
                heading="Brand line items"
                downloadLineItemData={props.downloadLineItemData}
            />
        );
        expect(screen.getByText('Brand line items')).toBeInTheDocument();
    });

    it(`${prefix} renders brand line items download button`, async () => {
        //mock configHelpers.isServiceProvider()
        ConfigHelpers.isServiceProvider.mockReturnValue(false);

        render(
            <LineItems
                config={props.config}
                srvcConfigData={props.srvcConfigData}
                lineItemsData={props.lineItemsData}
                isBrand
                readOnly={props.readOnly}
                isSrvcReqLock={props.isSrvcReqLock}
                revisionsData={props.revisionsData}
                title={props.title}
                heading="Brand line items"
                downloadLineItemData={props.downloadLineItemData}
            />
        );
        expect(screen.getByText('Brand line items')).toBeInTheDocument();

        // Find the download button
        const downloadButton = screen.getByRole('button', {
            name: /download/i,
        });
        expect(downloadButton).toBeInTheDocument();
    });

    it(`${prefix} renders brand line items download button in sp customer access`, () => {
        // mock configHelpers.isServiceProvider()
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        render(
            <LineItems
                config={props.config}
                srvcConfigData={props.srvcConfigData}
                lineItemsData={props.lineItemsData}
                isBrand
                readOnly={props.readOnly}
                isSrvcReqLock={props.isSrvcReqLock}
                revisionsData={props.revisionsData}
                title={props.title}
                heading="Brand line items"
                downloadLineItemData={props.downloadLineItemData}
                isCustAccess={props.isCustAcces}
            />
        );
        expect(screen.getByText('Brand line items')).toBeInTheDocument();

        // Find the download button
        const downloadButton = screen.getByRole('button', {
            name: /download/i,
        });
        expect(downloadButton).toBeInTheDocument();
    });

    it(`${prefix} renders brand line items download button in sp if Show line items to SP is enabled`, () => {
        // mock configHelpers.isServiceProvider()
        ConfigHelpers.isServiceProvider.mockReturnValue(true);
        render(
            <LineItems
                config={props.config}
                srvcConfigData={props.srvcConfigData}
                lineItemsData={props.lineItemsData}
                isBrand
                readOnly={props.readOnly}
                isSrvcReqLock={props.isSrvcReqLock}
                revisionsData={props.revisionsData}
                title={props.title}
                heading="Brand line items"
                downloadLineItemData={props.downloadLineItemData}
                isCustAccess={false}
            />
        );
        expect(screen.getByText('Brand line items')).toBeInTheDocument();

        // Find the download button
        const downloadButton = screen.getByRole('button', {
            name: /download/i,
        });
        expect(downloadButton).toBeInTheDocument();
    });

    // write test to check if  srvc provider line items heding is visible
    it(`${prefix} renders srvc provider line items heading`, () => {
        render(
            <LineItems
                config={props.config}
                srvcConfigData={props.srvcConfigData}
                lineItemsData={props.lineItemsData}
                isBrand
                readOnly={props.readOnly}
                isSrvcReqLock={props.isSrvcReqLock}
                revisionsData={props.revisionsData}
                title={props.title}
                heading="Service provider line items"
                downloadLineItemData={props.downloadLineItemData}
                allow_sp_line_items_download={
                    props.allow_sp_line_items_download
                }
            />
        );
        expect(
            screen.getByText('Service provider line items')
        ).toBeInTheDocument();
    });
});
