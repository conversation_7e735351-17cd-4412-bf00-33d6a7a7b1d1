.availability-calendar .calendar-day {
    transition: all 0.2s ease;
}

.availability-calendar .calendar-day.today.selected,
.availability-calendar .calendar-day.today {
    background-color: #bbdefb;
    border: 1px solid #038fde;
    font-weight: 500;
}
.availability-calendar .calendar-day.today span.ant-typography,
.availability-calendar .calendar-day.today.selected span.ant-typography {
    color: #038fde !important;
}
.availability-calendar .calendar-day.selected {
    border: 1px solid #038fde;
    font-weight: 500;
}
.availability-calendar .calendar-day.selected span.ant-typography {
    color: #038fde !important;
}

.availability-calendar .calendar-grid-wrapper {
    border-radius: 6px;
    overflow: hidden;
}

/* Selected date details section */
.availability-calendar .selected-date-header {
    text-align: center;
    background: linear-gradient(133deg, #18d2ea 0%, #40a9ff 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    margin: -24px -24px 0 -24px;
}

.availability-calendar .selected-date-header .ant-typography {
    color: white !important;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .availability-calendar .calendar-day {
        min-height: 30px;
        font-size: 12px;
    }

    .availability-calendar .gx-py-3 {
        padding: 8px 4px;
    }

    .availability-calendar .selected-date-header {
        margin: -16px -16px 0 -16px;
    }
}

.availability-calendar .availability-calendar-card-wrapper .ant-card-body {
    padding: 0 0 10px 0;
}

.calendar-legend {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 1rem;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 6px;
}

.legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.legend-label {
    font-size: 12px;
}
.calendar-day-fixed {
    width: 100%;
    max-width: 36px;
    padding: 5px 0;
}

.wy-calendar-day-success {
    background-color: #77f996; /* Light green for success */
}

.wy-calendar-day-warning {
    background-color: #ff9800; /* Light yellow for warning */
}

.wy-calendar-day-red {
    background-color: #f37681; /* Light red for error */
}

.wy-calendar-day-default {
    background-color: #7ba4f6; /* Light grey for default */
}
.calendar-grid {
    display: grid;
    grid-template-columns: repeat(
        7,
        1fr
    ); /* Repeat 7 times, each column takes equal space */
    gap: 8px; /* Optional: space between grid items */
}

.ant-tabs-tab-active .wy-amya-day-date {
    color: #fff;
    border-radius: 5px;
    background: #038fde;
}

.wy-amya-day-date {
    height: 30px !important;
    width: 30px !important;
    margin: 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 3px;
}

.wy-amya-calendar-wrapper .ant-tabs-tab {
    padding: 0 0 10px 0;
}

.save-new-availability-slots {
    position: fixed;
    bottom: -100px;
    left: 50%;
    transform: translateX(-50%);
    color: white;
    border-radius: 8px;
    transition: bottom 0.3s ease;
    z-index: 999;
    width: 100%;
}

.save-new-availability-slots.show {
    animation: slideUp 0.4s ease-out forwards;
}

@keyframes slideUp {
    from {
        bottom: -100px;
        opacity: 0;
    }
    to {
        bottom: 0px;
        opacity: 1;
    }
}

.buttons-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.wy-amya-slots-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.wy-amya-tab-width {
    width: 4.2rem;
}

.wy-amya-slots-wrapper .ant-card-body {
    padding: 18px;
}

