import React, { useEffect, useState } from 'react';
import http_utils from '../../util/http_utils';
import {
    Spin,
    Card,
    Button,
    Tag,
    Typography,
    Row,
    Col,
    Alert,
    message,
} from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import CircularProgress from '../../components/CircularProgress';
import { PiAirplaneLight, PiAirTrafficControl } from 'react-icons/pi';
import { canUpdateDate, getStatusCode, isSlotInPast } from './helpers';
import './calendar.css';

const { Text } = Typography;
const protoUrl = '/my-availability/day';
const submitUrl = '/my-availability';

const SingleDay = ({ day, onNewStatus }) => {
    const { date } = day;
    const [viewData, setViewData] = useState(null);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [error, setError] = useState(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedSlots, setSelectedSlots] = useState({});
    const [unSavedChanges, setUnSavedChanges] = useState(false);

    useEffect(() => {
        initViewData();
        setUnSavedChanges(false);
    }, [date]);

    useEffect(() => {
        // Get total number of slots
        const totalSlots = Object.keys(selectedSlots).length;
        if (totalSlots == 0) return;
        // Get number of slots marked as available
        const availableSlots = Object.values(selectedSlots).filter(
            (isAvailable) => isAvailable
        ).length;
        onNewStatus(getStatusCode(totalSlots, availableSlots));
        // message.info(
        //     `${availableSlots} out of ${totalSlots} slots marked as available`
        // );
    }, [selectedSlots]);

    const initViewData = () => {
        setIsLoadingViewData(true);
        const onComplete = (resp) => {
            setIsLoadingViewData(false);
            setViewData(resp.data);
            initializeSelectedSlots(resp.data?.slots);
        };

        const onError = (error) => {
            setIsLoadingViewData(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performGetCall(protoUrl, { date }, onComplete, onError);
    };

    const { slots = [] } = viewData || {};

    const initializeSelectedSlots = (slots) => {
        const initSlots = {};
        slots.forEach(({ db_id, is_available }) => {
            initSlots[db_id] = is_available || false;
        });
        setSelectedSlots(initSlots);
    };

    const toggleSlot = (db_id) => {
        setSelectedSlots((prev) => ({ ...prev, [db_id]: !prev[db_id] }));
        setUnSavedChanges(true);
    };

    const updateAllSlots = (status) => {
        if (!viewData) return;
        const all = {};
        slots.forEach(({ db_id }) => (all[db_id] = status));
        setSelectedSlots(all);
        setUnSavedChanges(true);
    };

    const submit = () => {
        setIsSubmitting(true);

        let params = {
            day: date,
            slots: slots.map((slot) => ({
                ...slot,
                is_available: selectedSlots[slot.db_id],
            })),
        };
        const onComplete = (resp) => {
            setIsSubmitting(false);
            setUnSavedChanges(false);
        };

        const onError = (error) => {
            setIsSubmitting(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performPostCall(submitUrl, params, onComplete, onError);
    };

    if (isLoadingViewData) {
        return (
            <div className="gx-loader-view gx-loader-position">
                <CircularProgress />
            </div>
        );
    } else if (viewData == null) {
        return <p className="gx-text-red">{error}</p>;
    } else if (viewData == 'capacity_details_missing') {
        return (
            <div className="wy-without-access-wrapper">
                <PiAirplaneLight className="gx-fs-iconcard gx-text-grey" />
                <p className="gx-text-black">
                    Your account does not have key capacity mappings. <br />
                    Kindly contact admin to know more.
                </p>
            </div>
        );
    }

    const canUpdate = canUpdateDate(day.date, slots);

    return (
        <div className="gx-pb-3">
            {unSavedChanges && (
                <div className="save-new-availability-slots show">
                    <Alert
                        type="warning"
                        showIcon
                        className="gx-mb-0"
                        message={
                            <div className="gx-d-flex gx-justify-content-between gx-align-items-center">
                                <h4 className="gx-mb-0">
                                    You have unsaved changes{' '}
                                </h4>
                                <div>
                                    {isSubmitting ? (
                                        <Spin />
                                    ) : (
                                        <Button
                                            type="primary"
                                            onClick={submit}
                                            className="gx-mb-0 gx-px-3 "
                                        >
                                            Save
                                        </Button>
                                    )}
                                </div>
                            </div>
                        }
                    />
                </div>
            )}
            {canUpdate ? (
                <div className="gx-text-right">
                    <Button
                        size="small"
                        ghost
                        className="wy-btn-outline-success wy-amya-available-all-day"
                        onClick={() => updateAllSlots(true)}
                    >
                        <CheckCircleOutlined /> Mark Present
                    </Button>
                    <Button
                        size="small"
                        className="wy-btn-outline-error wy-amya-unavailable-all-day"
                        danger
                        onClick={() => updateAllSlots(false)}
                    >
                        <CloseCircleOutlined /> Mark Absent
                    </Button>
                </div>
            ) : (
                <div>
                    <Alert
                        type="error"
                        message="You cannot update availability for past dates."
                    ></Alert>
                </div>
            )}
            <div className="wy-amya-slots-wrapper">
                {slots.map((slot) => {
                    const isAvailable = selectedSlots[slot.db_id];
                    const isPastSlot = isSlotInPast(day.date, slot);
                    const canEditSlot = canUpdate && !isPastSlot;
                    return (
                        <Card
                            key={slot.db_id}
                            onClick={
                                canEditSlot
                                    ? () => toggleSlot(slot.db_id)
                                    : undefined
                            }
                            style={{
                                backgroundColor: isAvailable
                                    ? '#f6ffed'
                                    : '#fafafa',
                                borderLeft: `5px solid ${isAvailable ? '#52c41a' : '#d9d9d9'}`,
                                marginBottom: 8,
                                cursor: 'pointer',
                            }}
                        >
                            <div className="gx-d-flex gx-justify-content-between gx-align-items-center">
                                <div>
                                    <Text strong>
                                        {slot.start_time} - {slot.end_time}
                                    </Text>
                                </div>
                                {canUpdate && (
                                    <Tag
                                        color={isAvailable ? 'green' : 'gray'}
                                        className="gx-m-0"
                                    >
                                        {isAvailable ? (
                                            <>
                                                <CheckCircleOutlined />{' '}
                                                Available
                                            </>
                                        ) : (
                                            <>
                                                <CloseCircleOutlined />{' '}
                                                Unavailable
                                            </>
                                        )}
                                    </Tag>
                                )}
                                {/* {!canEditSlot && (
                                    <Tag color="default" className="gx-m-0">
                                        <CloseCircleOutlined />
                                        Cannot Select
                                    </Tag>
                                )} */}
                            </div>
                        </Card>
                    );
                })}
            </div>
        </div>
    );
};

export default SingleDay;
