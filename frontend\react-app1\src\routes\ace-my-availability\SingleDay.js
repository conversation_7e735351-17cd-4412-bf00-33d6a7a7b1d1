import React, { useEffect, useState } from 'react';
import http_utils from '../../util/http_utils';
import {
    Spin,
    Card,
    Button,
    Tag,
    Typography,
    Row,
    Col,
    Alert,
    message,
} from 'antd';
import { CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import CircularProgress from '../../components/CircularProgress';
import { PiAirplaneLight, PiAirTrafficControl } from 'react-icons/pi';
import { canUpdateDate, getStatusCode, isSlotInPast } from './helpers';

const { Text } = Typography;
const protoUrl = '/my-availability/day';
const submitUrl = '/my-availability';

const SingleDay = ({ day, onNewStatus }) => {
    const { date } = day;
    const [viewData, setViewData] = useState(null);
    const [isLoadingViewData, setIsLoadingViewData] = useState(false);
    const [error, setError] = useState(null);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [selectedSlots, setSelectedSlots] = useState({});
    const [unSavedChanges, setUnSavedChanges] = useState(false);

    useEffect(() => {
        initViewData();
        setUnSavedChanges(false);
    }, [date]);

    useEffect(() => {
        // Get total number of slots
        const totalSlots = Object.keys(selectedSlots).length;
        if (totalSlots == 0) return;
        // Get number of slots marked as available
        const availableSlots = Object.values(selectedSlots).filter(
            (isAvailable) => isAvailable
        ).length;
        onNewStatus(getStatusCode(totalSlots, availableSlots));
        // message.info(
        //     `${availableSlots} out of ${totalSlots} slots marked as available`
        // );
    }, [selectedSlots]);

    const initViewData = () => {
        setIsLoadingViewData(true);
        const onComplete = (resp) => {
            setIsLoadingViewData(false);
            setViewData(resp.data);
            initializeSelectedSlots(resp.data?.slots);
        };

        const onError = (error) => {
            setIsLoadingViewData(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performGetCall(protoUrl, { date }, onComplete, onError);
    };

    const { slots = [] } = viewData || {};

    const initializeSelectedSlots = (slots) => {
        const initSlots = {};
        slots.forEach(({ db_id, is_available }) => {
            initSlots[db_id] = is_available || false;
        });
        setSelectedSlots(initSlots);
    };

    const toggleSlot = (db_id) => {
        setSelectedSlots((prev) => ({ ...prev, [db_id]: !prev[db_id] }));
        setUnSavedChanges(true);
    };

    const updateAllSlots = (status) => {
        if (!viewData) return;
        const all = {};
        slots.forEach(({ db_id }) => (all[db_id] = status));
        setSelectedSlots(all);
        setUnSavedChanges(true);
    };

    const submit = () => {
        setIsSubmitting(true);

        let params = {
            day: date,
            slots: slots.map((slot) => ({
                ...slot,
                is_available: selectedSlots[slot.db_id],
            })),
        };
        const onComplete = (resp) => {
            setIsSubmitting(false);
            setUnSavedChanges(false);
        };

        const onError = (error) => {
            setIsSubmitting(false);
            setError(http_utils.decodeErrorToMessage(error));
        };
        http_utils.performPostCall(submitUrl, params, onComplete, onError);
    };

    if (isLoadingViewData) {
        return (
            <div className="gx-loader-view gx-loader-position">
                <CircularProgress />
            </div>
        );
    } else if (viewData == null) {
        return <p className="gx-text-red">{error}</p>;
    } else if (viewData == 'capacity_details_missing') {
        return (
            <div className="wy-without-access-wrapper">
                <PiAirplaneLight className="gx-fs-iconcard gx-text-grey" />
                <p className="gx-text-black">
                    Your account does not have key capacity mappings. <br />
                    Kindly contact admin to know more.
                </p>
            </div>
        );
    }
    console.log('viewData', viewData);
    const canUpdate = canUpdateDate(day.date, slots);

    return (
        <div className="gx-px-1">
            {unSavedChanges && (
                <Alert
                    type="warning"
                    message={
                        <div>
                            <h3>You have unsaved changes</h3>
                            <div>
                                {isSubmitting ? (
                                    <Spin />
                                ) : (
                                    <Button type="primary" onClick={submit}>
                                        SAVE
                                    </Button>
                                )}
                            </div>
                        </div>
                    }
                />
            )}
            {canUpdate ? (
                <div>
                    <Button onClick={() => updateAllSlots(true)}>
                        Available All Day
                    </Button>
                    <Button danger onClick={() => updateAllSlots(false)}>
                        Unavailable All Day
                    </Button>
                </div>
            ) : (
                <div>
                    <Alert
                        type="error"
                        message="You cannot update availability for past dates."
                    ></Alert>
                </div>
            )}

            {slots.map((slot) => {
                const isAvailable = selectedSlots[slot.db_id];
                const isPastSlot = isSlotInPast(day.date, slot);
                const canEditSlot = canUpdate && !isPastSlot;
                return (
                    <Card
                        key={slot.db_id}
                        onClick={
                            canEditSlot
                                ? () => toggleSlot(slot.db_id)
                                : undefined
                        }
                        style={{
                            backgroundColor: isAvailable
                                ? '#f6ffed'
                                : '#fafafa',
                            borderLeft: `5px solid ${isAvailable ? '#52c41a' : '#d9d9d9'}`,
                            marginBottom: 8,
                            cursor: 'pointer',
                        }}
                    >
                        <Row justify="space-between" align="middle">
                            <Col>
                                <Text strong>
                                    {slot.start_time} - {slot.end_time}
                                </Text>
                            </Col>
                            {canEditSlot && (
                                <Tag color={isAvailable ? 'green' : 'gray'}>
                                    {isAvailable ? (
                                        <CheckCircleOutlined />
                                    ) : (
                                        <CloseCircleOutlined />
                                    )}
                                    {isAvailable
                                        ? ' Available'
                                        : ' Unavailable'}
                                </Tag>
                            )}
                            {!canEditSlot && (
                                <Tag color="default">
                                    <CloseCircleOutlined /> Not Editable
                                </Tag>
                            )}
                        </Row>
                    </Card>
                );
            })}
        </div>
    );
};

export default SingleDay;
