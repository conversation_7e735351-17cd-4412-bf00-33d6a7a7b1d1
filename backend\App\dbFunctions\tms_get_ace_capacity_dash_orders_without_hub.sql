CREATE OR REPLACE FUNCTION public.tms_get_ace_capacity_dash_orders_without_hub(form_data json)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    status bool;
    message text;
    resp_data json;
    org_id_ integer;
    vertical_id_ integer;
    usr_id_ uuid;
    ip_address_ text;
    user_agent_ text;
    org_level_setting_data json;
    org_level_set_country_code text;

BEGIN
    status = false;
    message = 'Internal_error';
    resp_data = '{}'::json;

    -- Extract parameters from form_data
    org_id_ = (form_data->>'org_id')::integer;
    vertical_id_ = (form_data->>'vertical_id')::integer;
    usr_id_ = form_data->>'usr_id';
    ip_address_ = form_data->>'ip_address';
    user_agent_ = form_data->>'user_agent';

    -- Get organization's country code from settings
    org_level_setting_data := tms_hlpr_get_org_level_settings_config_data_for_org(org_id_)->'data';
    org_level_set_country_code := org_level_setting_data->>'select_country_code';
  
    -- Check if capacity module is enabled for this organization
    IF NOT EXISTS (
        SELECT 1
          FROM public.cl_tx_orgs_settings
         WHERE org_id = org_id_
           AND settings_type = 'ACE_CAPACITY_SETTINGS'
           AND (settings_data->>'enable_capacity_module')::boolean = true
    ) THEN
        message := 'capacity_module_disabled';
        RETURN json_build_object('status', status, 'code', message, 'data', null);
    END IF;


    -- Get all srvc req count that are not in any service hub for the given vertical
	 resp_data =  array_to_json(array(
	       SELECT jsonb_build_object(
			    'state', loc_mstr.state,
			    'city', loc_mstr.dist,
			    'count',count(distinct req.db_id)			 
			)
			 FROM cl_tx_srvc_req req
			 left join sys_cf_loc_mstr as loc_mstr
   	           on loc_mstr.id = (
					select id 
					  from sys_cf_loc_mstr 
					 where pincode = req.cust_pincode 
					 limit 1
			      )
			inner join cl_cf_srvc_statuses req_status 
			   on req_status.srvc_id = req.srvc_type_id 
		      and req_status.status_key = req.status    
			WHERE req.prvdr_vertical  = vertical_id_
			  AND req.prvdr_srvc_hub is null
			  and req.cust_pincode is not null
			  and req.is_deleted is not true 
			  and req.srvc_prvdr = org_id_
			  AND loc_mstr.country_code = COALESCE(org_level_set_country_code, 'IN')
			GROUP BY loc_mstr.state, loc_mstr.dist
			ORDER BY loc_mstr.state, loc_mstr.dist


	    ));
    -- If no data found, return empty array
    IF resp_data IS NULL THEN
        resp_data := '[]'::json;
    END IF;
     
    status := TRUE;
    message := 'success';

    RETURN json_build_object('status', status,
                             'code', message,
                             'data', resp_data);
END;
$function$
;
