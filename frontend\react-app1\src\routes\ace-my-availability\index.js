import { Card, message, Tabs, Tag, Typography } from 'antd';
import React, { useState } from 'react';
import { convertUTCToDisplayTime, isTodayDate } from '../../util/helpers';
import SingleDay from './SingleDay';
import { CheckOutlined } from '@ant-design/icons';
import moment from 'moment';
import { availabilityStatuses } from './helpers';
import CalendarView from './calender';

const Text = Typography.Text;

const generateDays = () => {
    const days = [];
    const currentDate = moment();
    for (let i = 0; i < 4; i++) {
        const date = moment(currentDate).add(i, 'days');
        const dateStr = date.local().format('YYYY-MM-DD');
        const dayName = isTodayDate(dateStr) ? 'Today' : date.format('ddd');
        days.push({
            dayName,
            date: dateStr,
        });
    }
    return days;
};

const MyAvailability = () => {
    const [activeMainTab, setActiveMainTab] = useState('calendar');
    const [selectedDay, setSelectedDay] = useState('1');
    const [days, setdays] = useState(generateDays());
    const [dayWiseStatus, setDayWiseStatus] = useState({});

    return (
        <Card>
            <Tabs
                activeKey={activeMainTab}
                onChange={(key) => setActiveMainTab(key)}
            >
                <Tabs.TabPane tab="Calendar" key="calendar">
                    <CalendarView />
                </Tabs.TabPane>
                <Tabs.TabPane tab="My Availability" key="availability">
                    <Tabs activeKey={selectedDay} onChange={setSelectedDay}>
                        {days.map((day, dayIndex) => {
                            const status =
                                dayWiseStatus[day.date] ||
                                availabilityStatuses.offline.value;
                            return (
                                <Tabs.TabPane
                                    forceRender
                                    key={String(dayIndex + 1)}
                                    tab={
                                        <div>
                                            <Text strong>{day.dayName} </Text>
                                            <Tag
                                                color={
                                                    availabilityStatuses[status]
                                                        .color
                                                }
                                                className="m-0"
                                            >
                                                {
                                                    availabilityStatuses[status]
                                                        .label
                                                }
                                            </Tag>
                                            <div className="text-xs text-gray-500">
                                                {convertUTCToDisplayTime(
                                                    day.date,
                                                    true
                                                )}
                                            </div>
                                        </div>
                                    }
                                >
                                    <SingleDay
                                        day={day}
                                        onNewStatus={(statusCode) =>
                                            setDayWiseStatus({
                                                ...dayWiseStatus,
                                                [day.date]: statusCode,
                                            })
                                        }
                                    />
                                </Tabs.TabPane>
                            );
                        })}
                    </Tabs>
                </Tabs.TabPane>
            </Tabs>
        </Card>
    );
};

export default MyAvailability;
