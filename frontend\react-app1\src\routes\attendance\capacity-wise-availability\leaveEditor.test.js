import React from 'react';
import { render } from '@testing-library/react';
import leaveEditor from './leaveEditor';
beforeEach(() => {
    console.info('Console logs disabled!');
    jest.spyOn(console, 'log').mockImplementation(() => {});
    jest.spyOn(console, 'group').mockImplementation(() => {});
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'warn').mockImplementation(() => {});
});
describe('Smoke Test for leaveEditor', () => {
    it('renders without crashing', () => {
        render(<leaveEditor />);
    });
});
