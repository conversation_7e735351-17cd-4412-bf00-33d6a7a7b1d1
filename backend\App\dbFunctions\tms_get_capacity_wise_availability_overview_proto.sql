CREATE OR REPLACE FUNCTION public.tms_get_capacity_wise_availability_overview_proto (form_data json) RETURNS json LANGUAGE plpgsql AS $function$
declare 
	status boolean;
	message text;
	affected_rows integer;
	resp_data json;
	--form data
	org_id_ integer;
	usr_id_ uuid;
	ip_address_ text;
	user_agent_ text;
	filters_ json;

	total_capacity integer;
	available_capacity integer;
	utilized_capacity integer;
	utilization_percentage numeric;

begin

	status = false;
	message = 'Internal_error';
	--form data 
	usr_id_ = form_data->>'usr_id';
	ip_address_ = form_data->>'ip_address';  
	user_agent_ = form_data->>'user_agent';
	org_id_ = (form_data->>'org_id')::integer;
	filters_ = form_data->'filters';

	-- Calculate capacity metrics
	select 
		count(*) as total_users,
		count(case when availability.is_available = true then 1 end) as available_users,
		count(case when availability.is_available = false then 1 end) as utilized_users
	from 
		cl_tx_usr_availability as availability
	inner join 
		cl_tx_users as users on users.user_id = availability.user_id
	where 
		users.org_id = org_id_
		and availability.is_active = true
		and availability.usr_tmzone_day = current_date
	into 
		total_capacity, available_capacity, utilized_capacity;

	-- Calculate utilization percentage
	if total_capacity > 0 then
		utilization_percentage = (utilized_capacity::numeric / total_capacity::numeric) * 100;
	else
		utilization_percentage = 0;
	end if;

	status = true;
	message = 'success';
	resp_data := jsonb_build_object(
		'status', status,
		'code', message,
		'data', jsonb_build_object(
			'total_capacity', total_capacity,
			'available_capacity', available_capacity,
			'utilized_capacity', utilized_capacity,
			'utilization_percentage', round(utilization_percentage, 2),
			'date', current_date
		)
	);

	return resp_data;

end;
$function$;
