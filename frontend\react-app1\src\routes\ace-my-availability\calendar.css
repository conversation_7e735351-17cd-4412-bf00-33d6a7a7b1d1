.availability-calendar .calendar-day {
    transition: all 0.2s ease;
}

.availability-calendar .calendar-day:hover {
    background-color: #f5f5f5 !important;
    transform: scale(1.02);
}

.availability-calendar .calendar-day.today {
    border: 2px solid #1890ff !important;
    font-weight: bold;
}

.availability-calendar .calendar-grid {
    border: 1px solid #f0f0f0;
    border-radius: 6px;
    overflow: hidden;
}

.availability-calendar .calendar-day {
    border-right: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
}

.availability-calendar .calendar-day:last-child {
    border-right: none;
}

.availability-calendar .calendar-day:hover:not(.gx-text-grey) {
    background-color: #e6f7ff !important;
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
    .availability-calendar .calendar-day {
        min-height: 40px;
        font-size: 12px;
    }
    
    .availability-calendar .gx-py-3 {
        padding: 8px 4px;
    }
}
